# src/ruby/photon_render/dialog.rb
# PhotonRender - SketchUp Dialog System
# Sistema dialog per impostazioni e progress PhotonRender

module PhotonRender
  module Dialog
    
    # Show render settings dialog
    def self.show_render_settings(&callback)
      puts "Showing render settings dialog..."
      
      # Create dialog HTML
      html_content = create_render_settings_html
      
      # Create web dialog
      dialog = UI::WebDialog.new("PhotonRender Settings", true, "photon_render_settings", 600, 500, 100, 100, true)
      dialog.set_html(html_content)
      
      # Handle dialog callbacks
      setup_render_settings_callbacks(dialog, callback)
      
      dialog.show
      @render_settings_dialog = dialog
    end
    
    # Show render progress dialog
    def self.show_render_progress
      puts "Showing render progress dialog..."
      
      html_content = create_render_progress_html
      
      dialog = UI::WebDialog.new("PhotonRender Progress", false, "photon_render_progress", 400, 300, 200, 200, false)
      dialog.set_html(html_content)
      
      setup_render_progress_callbacks(dialog)
      
      dialog.show
      @render_progress_dialog = dialog
    end
    
    # Hide render progress dialog
    def self.hide_render_progress
      if @render_progress_dialog
        @render_progress_dialog.close
        @render_progress_dialog = nil
      end
    end
    
    # Update render progress
    def self.update_progress(progress, stats = {})
      if @render_progress_dialog
        # Send progress update to dialog
        js_code = "updateProgress(#{progress}, #{stats.to_json});"
        @render_progress_dialog.execute_script(js_code)
      end
    end
    
    # Show material editor dialog
    def self.show_material_editor
      puts "Showing PhotonRender Material Editor..."

      # Load HTML content from file
      html_file = File.join(File.dirname(__FILE__), 'material_editor.html')

      if File.exist?(html_file)
        html_content = File.read(html_file)
      else
        puts "Warning: material_editor.html not found, using fallback"
        html_content = create_fallback_material_editor_html
      end

      # Create dialog with larger size for material editor
      dialog = UI::WebDialog.new("PhotonRender Material Editor", true, "photon_render_materials", 1200, 800, 50, 50, true)
      dialog.set_html(html_content)

      # Setup callbacks for material editor
      setup_material_editor_callbacks(dialog)

      dialog.show
      @material_editor_dialog = dialog

      puts "Material Editor dialog opened successfully"
    end
    
    # Show render queue dialog
    def self.show_render_queue
      puts "Showing render queue dialog..."
      UI.messagebox("Render queue not yet implemented")
    end
    
    # Show light setup dialog
    def self.show_light_setup
      puts "Showing light setup dialog..."
      UI.messagebox("Light setup not yet implemented")
    end
    
    # Show environment settings dialog
    def self.show_environment_settings
      puts "Showing environment settings dialog..."
      UI.messagebox("Environment settings not yet implemented")
    end
    
    # Show preferences dialog
    def self.show_preferences
      puts "Showing preferences dialog..."
      UI.messagebox("Preferences not yet implemented")
    end
    
    private
    
    # Create render settings HTML
    def self.create_render_settings_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Settings</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .section { margin-bottom: 20px; }
            .section h3 { margin-top: 0; color: #333; border-bottom: 2px solid #4CAF50; padding-bottom: 5px; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
            .checkbox-group { display: flex; align-items: center; }
            .checkbox-group input { width: auto; margin-right: 10px; }
            .buttons { text-align: right; margin-top: 20px; }
            button { padding: 10px 20px; margin-left: 10px; border: none; border-radius: 4px; cursor: pointer; }
            .btn-primary { background: #4CAF50; color: white; }
            .btn-secondary { background: #6c757d; color: white; }
            .btn-primary:hover { background: #45a049; }
            .btn-secondary:hover { background: #5a6268; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Settings</h2>
            
            <div class="section">
              <h3>Image Settings</h3>
              <div class="form-group">
                <label for="width">Width:</label>
                <input type="number" id="width" value="1920" min="64" max="8192">
              </div>
              <div class="form-group">
                <label for="height">Height:</label>
                <input type="number" id="height" value="1080" min="64" max="8192">
              </div>
              <div class="form-group">
                <label for="samples">Samples per Pixel:</label>
                <input type="number" id="samples" value="100" min="1" max="10000">
              </div>
            </div>
            
            <div class="section">
              <h3>Rendering Settings</h3>
              <div class="form-group">
                <label for="bounces">Max Bounces:</label>
                <input type="number" id="bounces" value="8" min="1" max="50">
              </div>
              <div class="form-group">
                <label for="integrator">Integrator:</label>
                <select id="integrator">
                  <option value="path_tracing">Path Tracing</option>
                  <option value="direct_lighting">Direct Lighting</option>
                  <option value="ambient_occlusion">Ambient Occlusion</option>
                </select>
              </div>
              <div class="form-group checkbox-group">
                <input type="checkbox" id="use_gpu" checked>
                <label for="use_gpu">Use GPU Acceleration</label>
              </div>
              <div class="form-group checkbox-group">
                <input type="checkbox" id="enable_denoising" checked>
                <label for="enable_denoising">Enable AI Denoising</label>
              </div>
            </div>
            
            <div class="section">
              <h3>Performance Settings</h3>
              <div class="form-group">
                <label for="tile_size">Tile Size:</label>
                <select id="tile_size">
                  <option value="32">32x32</option>
                  <option value="64" selected>64x64</option>
                  <option value="128">128x128</option>
                  <option value="256">256x256</option>
                </select>
              </div>
              <div class="form-group">
                <label for="threads">CPU Threads:</label>
                <input type="number" id="threads" value="8" min="1" max="64">
              </div>
            </div>
            
            <div class="buttons">
              <button type="button" class="btn-secondary" onclick="cancelSettings()">Cancel</button>
              <button type="button" class="btn-primary" onclick="applySettings()">Start Render</button>
            </div>
          </div>
          
          <script>
            function applySettings() {
              const settings = {
                width: parseInt(document.getElementById('width').value),
                height: parseInt(document.getElementById('height').value),
                samples_per_pixel: parseInt(document.getElementById('samples').value),
                max_bounces: parseInt(document.getElementById('bounces').value),
                integrator: document.getElementById('integrator').value,
                use_gpu: document.getElementById('use_gpu').checked,
                enable_denoising: document.getElementById('enable_denoising').checked,
                tile_size: parseInt(document.getElementById('tile_size').value),
                threads: parseInt(document.getElementById('threads').value)
              };
              
              window.location = 'skp:apply_settings@' + JSON.stringify(settings);
            }
            
            function cancelSettings() {
              window.location = 'skp:cancel_settings@';
            }
          </script>
        </body>
        </html>
      HTML
    end
    
    # Create render progress HTML
    def self.create_render_progress_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Progress</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .progress-bar { width: 100%; height: 20px; background: #e0e0e0; border-radius: 10px; overflow: hidden; margin: 10px 0; }
            .progress-fill { height: 100%; background: #4CAF50; width: 0%; transition: width 0.3s ease; }
            .stats { margin-top: 15px; font-size: 14px; }
            .stat-item { margin: 5px 0; }
            .buttons { text-align: center; margin-top: 20px; }
            button { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; background: #f44336; color: white; }
            button:hover { background: #da190b; }
          </style>
        </head>
        <body>
          <div class="container">
            <h3>Rendering in Progress...</h3>
            
            <div class="progress-bar">
              <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div id="progressText">0%</div>
            
            <div class="stats">
              <div class="stat-item">Time Elapsed: <span id="timeElapsed">00:00</span></div>
              <div class="stat-item">Time Remaining: <span id="timeRemaining">--:--</span></div>
              <div class="stat-item">Samples: <span id="samples">0</span></div>
              <div class="stat-item">Rays/sec: <span id="raysPerSec">0</span></div>
            </div>
            
            <div class="buttons">
              <button onclick="stopRender()">Stop Render</button>
            </div>
          </div>
          
          <script>
            let startTime = Date.now();
            
            function updateProgress(progress, stats) {
              const progressPercent = Math.round(progress * 100);
              document.getElementById('progressFill').style.width = progressPercent + '%';
              document.getElementById('progressText').textContent = progressPercent + '%';
              
              // Update time
              const elapsed = Math.floor((Date.now() - startTime) / 1000);
              const minutes = Math.floor(elapsed / 60);
              const seconds = elapsed % 60;
              document.getElementById('timeElapsed').textContent = 
                String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
              
              // Update stats if provided
              if (stats.samples) {
                document.getElementById('samples').textContent = stats.samples;
              }
              if (stats.rays_per_sec) {
                document.getElementById('raysPerSec').textContent = Math.round(stats.rays_per_sec).toLocaleString();
              }
              
              // Estimate remaining time
              if (progress > 0.01) {
                const totalTime = elapsed / progress;
                const remaining = Math.floor(totalTime - elapsed);
                const remMinutes = Math.floor(remaining / 60);
                const remSeconds = remaining % 60;
                document.getElementById('timeRemaining').textContent = 
                  String(remMinutes).padStart(2, '0') + ':' + String(remSeconds).padStart(2, '0');
              }
            }
            
            function stopRender() {
              window.location = 'skp:stop_render@';
            }
          </script>
        </body>
        </html>
      HTML
    end
    
    # Create material editor HTML (simplified)
    def self.create_material_editor_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Material Editor</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            h2 { color: #333; margin-top: 0; }
            .placeholder { text-align: center; padding: 50px; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Material Editor</h2>
            <div class="placeholder">
              <h3>Material Editor</h3>
              <p>Advanced material editing interface will be implemented here.</p>
              <p>Features will include:</p>
              <ul style="text-align: left; display: inline-block;">
                <li>Disney PBR material editing</li>
                <li>Real-time material preview</li>
                <li>Texture mapping controls</li>
                <li>Material library management</li>
              </ul>
            </div>
          </div>
        </body>
        </html>
      HTML
    end
    
    # Setup render settings dialog callbacks
    def self.setup_render_settings_callbacks(dialog, callback)
      dialog.add_action_callback("apply_settings") do |web_dialog, action_name|
        begin
          settings_json = action_name.split('@', 2)[1]
          settings = JSON.parse(settings_json)
          
          puts "Render settings applied: #{settings}"
          
          dialog.close
          callback.call(settings) if callback
        rescue => e
          puts "Error applying settings: #{e.message}"
        end
      end
      
      dialog.add_action_callback("cancel_settings") do |web_dialog, action_name|
        puts "Render settings cancelled"
        dialog.close
        callback.call(nil) if callback
      end
    end
    
    # Setup render progress dialog callbacks
    def self.setup_render_progress_callbacks(dialog)
      dialog.add_action_callback("stop_render") do |web_dialog, action_name|
        puts "Stop render requested from dialog"
        PhotonRender.render_manager.stop_render
        dialog.close
      end
    end
    
    # Setup material editor dialog callbacks
    def self.setup_material_editor_callbacks(dialog)
      puts "Setting up Material Editor callbacks..."

      # Callback for rendering preview
      dialog.add_action_callback("renderPreview") do |dialog, params|
        begin
          puts "Rendering preview with params: #{params}"

          # Parse material data from JavaScript
          material_data = JSON.parse(params)

          # Create preview renderer if not exists
          @preview_renderer ||= create_preview_renderer

          # Update material and settings
          update_preview_material(material_data['material'])
          update_preview_geometry(material_data['geometry'])
          update_preview_settings(material_data['settings'])

          # Render preview asynchronously
          render_preview_async(dialog)

        rescue => e
          puts "Error in renderPreview callback: #{e.message}"
          puts e.backtrace
        end
      end

      # Callback for opening texture dialog
      dialog.add_action_callback("openTextureDialog") do |dialog, texture_type|
        begin
          puts "Opening texture dialog for: #{texture_type}"

          # Open file dialog for texture selection
          texture_path = UI.openpanel("Select Texture", "", "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.tga;*.hdr;*.exr||")

          if texture_path
            # Load texture and update material
            load_texture_for_material(texture_path, texture_type)

            # Notify JavaScript of texture load
            js_code = "window.materialEditor.onTextureLoaded('#{texture_type}', '#{texture_path}');"
            dialog.execute_script(js_code)
          end

        rescue => e
          puts "Error in openTextureDialog callback: #{e.message}"
        end
      end

      # Callback for saving material
      dialog.add_action_callback("saveMaterial") do |dialog, material_data|
        begin
          puts "Saving material: #{material_data}"

          # Parse material data
          data = JSON.parse(material_data)

          # Save material to library
          save_material_to_library(data)

          # Show success message
          UI.messagebox("Material saved successfully!", MB_OK)

        rescue => e
          puts "Error in saveMaterial callback: #{e.message}"
          UI.messagebox("Error saving material: #{e.message}", MB_OK)
        end
      end

      # Callback for applying material to selection
      dialog.add_action_callback("applyMaterial") do |dialog, material_data|
        begin
          puts "Applying material to selection: #{material_data}"

          # Parse material data
          material_params = JSON.parse(material_data)

          # Apply material to selected entities
          apply_material_to_selection(material_params)

          # Show success message
          UI.messagebox("Material applied to selection!", MB_OK)

        rescue => e
          puts "Error in applyMaterial callback: #{e.message}"
          UI.messagebox("Error applying material: #{e.message}", MB_OK)
        end
      end

      puts "Material Editor callbacks setup complete"
    end

    # Create preview renderer instance
    def self.create_preview_renderer
      puts "Creating preview renderer..."

      # Initialize preview renderer with default settings
      # This would interface with the C++ PreviewRenderer
      renderer = {
        width: 512,
        height: 512,
        samples: 16,
        geometry: 'sphere',
        material: create_default_material
      }

      puts "Preview renderer created"
      return renderer
    end

    # Update preview material parameters
    def self.update_preview_material(material_params)
      puts "Updating preview material: #{material_params}"

      # Convert JavaScript material parameters to C++ format
      @current_material = {
        'baseColor' => material_params['baseColor'] || [0.8, 0.2, 0.2],
        'metallic' => material_params['metallic'] || 0.0,
        'roughness' => material_params['roughness'] || 0.5,
        'specular' => material_params['specular'] || 0.5,
        'specularTint' => material_params['specularTint'] || 0.0,
        'anisotropic' => material_params['anisotropic'] || 0.0,
        'sheen' => material_params['sheen'] || 0.0,
        'sheenTint' => material_params['sheenTint'] || 0.5,
        'clearcoat' => material_params['clearcoat'] || 0.0,
        'clearcoatGloss' => material_params['clearcoatGloss'] || 1.0,
        'subsurface' => material_params['subsurface'] || 0.0
      }

      # TODO: Interface with C++ PreviewRenderer to update material
      puts "Material parameters updated"
    end

    # Update preview geometry
    def self.update_preview_geometry(geometry)
      puts "Updating preview geometry: #{geometry}"

      @current_geometry = geometry || 'sphere'

      # TODO: Interface with C++ PreviewRenderer to update geometry
      puts "Geometry updated to: #{@current_geometry}"
    end

    # Update preview settings
    def self.update_preview_settings(settings)
      puts "Updating preview settings: #{settings}"

      @preview_settings = {
        'width' => settings['width'] || 512,
        'height' => settings['height'] || 512,
        'samples' => settings['samples'] || 16
      }

      # TODO: Interface with C++ PreviewRenderer to update settings
      puts "Preview settings updated"
    end

    # Render preview asynchronously
    def self.render_preview_async(dialog)
      puts "Starting async preview render..."

      start_time = Time.now

      # TODO: Interface with C++ PreviewRenderer for actual rendering
      # For now, simulate rendering with a delay
      Thread.new do
        begin
          # Simulate render time
          sleep(0.5)

          render_time = ((Time.now - start_time) * 1000).round

          # Generate placeholder image data (base64 encoded)
          image_data = generate_placeholder_image

          # Call JavaScript callback with result
          js_code = "window.onPreviewComplete('#{image_data}', #{render_time});"
          dialog.execute_script(js_code)

          puts "Preview render completed in #{render_time}ms"

        rescue => e
          puts "Error in async render: #{e.message}"

          # Notify JavaScript of error
          js_code = "window.onPreviewComplete(null, 0);"
          dialog.execute_script(js_code)
        end
      end
    end

    # Load texture for material
    def self.load_texture_for_material(texture_path, texture_type)
      puts "Loading texture: #{texture_path} for #{texture_type}"

      # TODO: Interface with C++ texture loading system
      # For now, just store the path
      @material_textures ||= {}
      @material_textures[texture_type] = texture_path

      puts "Texture loaded: #{texture_type} -> #{texture_path}"
    end

    # Save material to library
    def self.save_material_to_library(material_data)
      puts "Saving material to library: #{material_data['name']}"

      # Create materials directory if it doesn't exist
      materials_dir = File.join(File.dirname(__FILE__), '..', '..', '..', 'assets', 'materials')
      Dir.mkdir(materials_dir) unless Dir.exist?(materials_dir)

      # Save material as JSON file
      material_file = File.join(materials_dir, "#{material_data['name']}.json")
      File.write(material_file, JSON.pretty_generate(material_data))

      puts "Material saved to: #{material_file}"
    end

    # Apply material to selected entities
    def self.apply_material_to_selection(material_params)
      puts "Applying material to selection..."

      model = Sketchup.active_model
      selection = model.selection

      if selection.empty?
        UI.messagebox("Please select some entities to apply the material to.", MB_OK)
        return
      end

      # Create SketchUp material from parameters
      materials = model.materials
      material_name = "PhotonRender_#{Time.now.to_i}"

      # Create new material
      material = materials.add(material_name)

      # Set basic properties
      base_color = material_params['baseColor'] || [0.8, 0.2, 0.2]
      color = Sketchup::Color.new(
        (base_color[0] * 255).to_i,
        (base_color[1] * 255).to_i,
        (base_color[2] * 255).to_i
      )
      material.color = color

      # Set alpha based on material properties
      alpha = 1.0
      if material_params['subsurface'] && material_params['subsurface'] > 0
        alpha = 0.9  # Slightly transparent for subsurface materials
      end
      material.alpha = alpha

      # Apply material to selection
      model.start_operation('Apply PhotonRender Material')

      selection.each do |entity|
        if entity.respond_to?(:material=)
          entity.material = material
        elsif entity.is_a?(Sketchup::Face)
          entity.material = material
        end
      end

      model.commit_operation

      puts "Material applied to #{selection.length} entities"
    end

    # Create default material
    def self.create_default_material
      {
        'baseColor' => [0.8, 0.2, 0.2],
        'metallic' => 0.0,
        'roughness' => 0.5,
        'specular' => 0.5,
        'specularTint' => 0.0,
        'anisotropic' => 0.0,
        'sheen' => 0.0,
        'sheenTint' => 0.5,
        'clearcoat' => 0.0,
        'clearcoatGloss' => 1.0,
        'subsurface' => 0.0
      }
    end

    # Generate placeholder image for preview
    def self.generate_placeholder_image
      # Return a simple base64 encoded placeholder image
      # In real implementation, this would come from the C++ renderer
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    end

    # Create fallback HTML if file not found
    def self.create_fallback_material_editor_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Material Editor</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #e0e0e0; }
            .container { background: #2a2a2a; padding: 20px; border-radius: 8px; }
            h2 { color: #ffffff; margin-top: 0; }
            .error { text-align: center; padding: 50px; color: #ff6b6b; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Material Editor</h2>
            <div class="error">
              <h3>Material Editor Loading Error</h3>
              <p>The material editor interface file could not be loaded.</p>
              <p>Please ensure material_editor.html is present in the plugin directory.</p>
            </div>
          </div>
        </body>
        </html>
      HTML
    end

  end
end
