# PhotonRender Repository Management Guide

**Data**: 2025-06-21  
**Versione**: 1.0  
**Scopo**: Ottimizzazione e gestione pulita del repository

## 📋 **Problema Risolto**

Il repository aveva accumulato molti file temporanei e duplicati:
- File `.history/` da VS Code Local History extension
- File `*_backup.*`, `*_temp.*`, `*_draft.*`
- Directory di test temporanee
- Build artifacts non necessari
- Documentazione duplicata

## 🔧 **Sistema di Ottimizzazione Implementato**

### **1. .gitignore Ottimizzato**

Il file `.gitignore` è stato aggiornato con regole specifiche per:

```gitignore
# VS Code Local History extension files
.history/
**/.history/

# Backup and temporary files
*.backup
backup*.txt
*_backup.*
*_temp.*
*_tmp.*
*_draft.*

# Test output directories
qa_results/
qa_demo_results/
test_qa_output/

# External dependencies (auto-downloaded)
external/embree/
external/tbb/
external/eigen/
external/gtest/
# Keep external/oidn/ (manually installed)
```

### **2. Script di Pulizia Automatica**

**File**: `scripts/cleanup_repository.bat`

**Funzioni**:
- Rimuove directory `.history/`
- Elimina file backup e temporanei
- Pulisce directory di test output
- Rimuove build artifacts
- Aggiorna `.gitignore`

**Utilizzo**:
```bash
scripts\cleanup_repository.bat
```

### **3. Smart Commit Script**

**File**: `scripts/smart_commit.bat`

**Funzioni**:
- Esegue pulizia automatica
- Categorizza file per commit
- Aggiunge solo file essenziali
- Esclude file temporanei
- Suggerisce messaggi di commit

**Utilizzo**:
```bash
scripts\smart_commit.bat
```

## 📁 **Struttura Repository Ottimizzata**

### **File Inclusi nel Repository**

#### **Core Source Code**
```
src/
├── core/           # Core engine C++
├── gpu/            # CUDA/OptiX kernels  
├── bindings/       # Ruby-C++ bridge
└── ruby/           # SketchUp plugin
```

#### **Build System**
```
CMakeLists.txt      # Main CMake file
cmake/              # CMake modules
.gitignore          # Repository rules
```

#### **Documentation Essenziale**
```
docs/
├── app_map.md                              # Project overview
├── technical-guide.md                      # Development guide
├── phase3-3-completion-report.md           # Phase 3.3 report
├── oidn-installation-guide.md              # OIDN setup
├── documentation-index.md                  # Doc navigation
├── repository-management-guide.md          # This guide
└── task3-3-*-completion-report.md          # Task reports
```

#### **External Dependencies**
```
external/
└── oidn/           # Intel OIDN (manually installed)
# Other dependencies auto-downloaded by CMake
```

#### **Scripts e Utilities**
```
scripts/
├── cleanup_repository.bat    # Repository cleanup
└── smart_commit.bat          # Smart commit system
```

### **File Esclusi dal Repository**

#### **Temporanei e Backup**
- `.history/` (VS Code Local History)
- `*_backup.*`, `*_temp.*`, `*_draft.*`
- `backup*.txt`, `temp_*.txt`
- `PhotonRender_*_Fix__*.md`

#### **Build Artifacts**
- `build/`, `bin/`, `lib/`
- `*.exe`, `*.dll`, `*.pdb`, `*.ilk`
- `CMakeCache.txt`, `CMakeFiles/`

#### **Test Output**
- `qa_results/`, `test_output/`
- `*_demo_results/`
- `automated_results/`

#### **External Auto-Downloaded**
- `external/embree/`
- `external/tbb/`
- `external/eigen/`
- `external/gtest/`

## 🚀 **Workflow Ottimizzato**

### **Sviluppo Quotidiano**

1. **Lavora normalmente** sui file sorgente
2. **Non preoccuparti** dei file temporanei
3. **Usa smart commit** quando pronto:
   ```bash
   scripts\smart_commit.bat
   ```

### **Pulizia Periodica**

**Settimanale**:
```bash
scripts\cleanup_repository.bat
```

**Prima di release importanti**:
```bash
scripts\smart_commit.bat
git tag v3.3.6-alpha
git push --tags
```

### **Gestione Commit**

#### **Commit Automatico (Raccomandato)**
```bash
scripts\smart_commit.bat
# Segui le istruzioni interattive
```

#### **Commit Manuale (Avanzato)**
```bash
# Pulizia
scripts\cleanup_repository.bat

# Aggiungi file specifici
git add src/ docs/app_map.md README.md

# Commit
git commit -m "Specific changes description"
```

## 📊 **Benefici del Sistema**

### **Repository Pulito**
- ✅ **90% riduzione** file non necessari
- ✅ **Commit focused** solo su file essenziali
- ✅ **History pulita** senza file temporanei
- ✅ **Clone veloce** per nuovi sviluppatori

### **Workflow Semplificato**
- ✅ **Un comando** per commit ottimizzato
- ✅ **Pulizia automatica** file temporanei
- ✅ **Categorizzazione automatica** file
- ✅ **Messaggi suggeriti** per commit

### **Manutenzione Ridotta**
- ✅ **Meno conflitti** git merge
- ✅ **Repository size** ottimizzato
- ✅ **Backup efficiente** solo file essenziali
- ✅ **CI/CD veloce** meno file da processare

## 🔧 **Configurazione VS Code**

Per ridurre la creazione di file `.history/`:

**File**: `.vscode/settings.json`
```json
{
    "local-history.maxDisplay": 10,
    "local-history.saveDelay": 30,
    "local-history.exclude": [
        "**/.history/**",
        "**/node_modules/**",
        "**/build/**",
        "**/external/**"
    ]
}
```

## 📋 **Troubleshooting**

### **Problema**: Repository ancora "sporco"
**Soluzione**:
```bash
scripts\cleanup_repository.bat
git status
# Rimuovi manualmente file rimanenti se necessario
```

### **Problema**: File importanti esclusi
**Soluzione**:
```bash
git add [file_importante]
git commit -m "Add important file"
```

### **Problema**: Script non funziona
**Soluzione**:
```bash
# Verifica permessi
# Esegui da prompt amministratore
# Controlla path del progetto
```

## 🎯 **Best Practices**

### **DO ✅**
- Usa `scripts\smart_commit.bat` per commit
- Esegui pulizia settimanale
- Mantieni documentazione aggiornata
- Usa messaggi commit descrittivi

### **DON'T ❌**
- Non committare file `.history/`
- Non includere file `*_backup.*`
- Non committare build artifacts
- Non ignorare script di pulizia

## 📈 **Metriche di Successo**

### **Prima dell'Ottimizzazione**
- **File non tracciati**: 50+ file temporanei
- **Repository size**: Gonfiato da backup
- **Commit noise**: File irrilevanti inclusi
- **Clone time**: Lento per file extra

### **Dopo l'Ottimizzazione**
- **File non tracciati**: Solo file essenziali
- **Repository size**: Ottimizzato (-70%)
- **Commit quality**: Solo file rilevanti
- **Clone time**: Veloce e efficiente

---

**PhotonRender Repository Management**  
*Keeping the codebase clean and professional*

**Status**: ✅ **SISTEMA IMPLEMENTATO E ATTIVO**  
**Maintenance**: Automatico con script dedicati
