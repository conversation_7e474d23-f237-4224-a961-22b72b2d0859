# src/ruby/photon_render/material_export_import_manager.rb
# PhotonRender - Material Export/Import Manager Ruby Interface
# Sistema di gestione export/import materiali per SketchUp

module PhotonRender
  module MaterialExportImportManager
    
    # Initialize material export/import manager
    def self.initialize
      puts "Initializing PhotonRender Material Export/Import Manager..."
      
      # Set default paths
      @export_directory = File.join(File.dirname(__FILE__), '..', '..', '..', 'exports')
      @import_directory = File.join(File.dirname(__FILE__), '..', '..', '..', 'imports')
      
      # Create directories if they don't exist
      Dir.mkdir(@export_directory) unless Dir.exist?(@export_directory)
      Dir.mkdir(@import_directory) unless Dir.exist?(@import_directory)
      
      # Initialize statistics
      @export_stats = {
        'total_exports' => 0,
        'successful_exports' => 0,
        'failed_exports' => 0,
        'total_materials_exported' => 0,
        'total_textures_exported' => 0
      }
      
      @import_stats = {
        'total_imports' => 0,
        'successful_imports' => 0,
        'failed_imports' => 0,
        'total_materials_imported' => 0,
        'total_textures_imported' => 0
      }
      
      puts "Material Export/Import Manager initialized successfully"
      puts "Export directory: #{@export_directory}"
      puts "Import directory: #{@import_directory}"
    end
    
    # Show material export/import dialog
    def self.show_export_import_interface
      puts "Showing Material Export/Import interface..."
      
      # Load HTML content
      html_file = File.join(File.dirname(__FILE__), 'material_export_import.html')
      
      if File.exist?(html_file)
        html_content = File.read(html_file)
      else
        puts "Warning: material_export_import.html not found, using fallback"
        html_content = create_fallback_export_import_html
      end
      
      # Create dialog
      dialog = UI::WebDialog.new("PhotonRender Material Export/Import", true, "photon_render_export_import", 1200, 800, 50, 50, true)
      dialog.set_html(html_content)
      
      # Setup callbacks
      setup_export_import_callbacks(dialog)
      
      dialog.show
      @export_import_dialog = dialog
      
      puts "Material Export/Import interface opened successfully"
    end
    
    # Get materials for export
    def self.get_materials_for_export
      puts "Getting materials for export..."
      
      # TODO: Interface with Material Library to get actual materials
      # For now, return mock materials
      materials = [
        {
          'id' => 'mat_001',
          'name' => 'Red Plastic',
          'type' => 'PBR Material',
          'textureCount' => 2,
          'parameters' => {
            'baseColor' => [0.8, 0.2, 0.2],
            'metallic' => 0.0,
            'roughness' => 0.3
          }
        },
        {
          'id' => 'mat_002',
          'name' => 'Chrome Metal',
          'type' => 'PBR Material',
          'textureCount' => 1,
          'parameters' => {
            'baseColor' => [0.9, 0.9, 0.9],
            'metallic' => 1.0,
            'roughness' => 0.05
          }
        },
        {
          'id' => 'mat_003',
          'name' => 'Wood Oak',
          'type' => 'PBR Material',
          'textureCount' => 4,
          'parameters' => {
            'baseColor' => [0.6, 0.4, 0.2],
            'metallic' => 0.0,
            'roughness' => 0.8
          }
        }
      ]
      
      puts "Found #{materials.size} materials for export"
      return materials
    end
    
    # Export materials
    def self.export_materials(options_json)
      begin
        options = JSON.parse(options_json)
        puts "Exporting materials with options: #{options}"
        
        # Get materials to export
        all_materials = get_materials_for_export
        selected_materials = all_materials.select { |mat| options['materialIds'].include?(mat['id']) }
        
        if selected_materials.empty?
          return {
            'success' => false,
            'errors' => ['No materials selected for export']
          }
        end
        
        # Prepare export
        export_result = {
          'success' => false,
          'materialCount' => selected_materials.size,
          'textureCount' => 0,
          'exportedFiles' => [],
          'exportTime' => 0.0,
          'warnings' => [],
          'errors' => []
        }
        
        start_time = Time.now
        
        # Export based on format
        case options['format']
        when 'json'
          export_result = export_to_json(selected_materials, options)
        when 'mtl'
          export_result = export_to_mtl(selected_materials, options)
        when 'gltf'
          export_result = export_to_gltf(selected_materials, options)
        when 'obj'
          export_result = export_to_obj_mtl(selected_materials, options)
        else
          export_result['errors'] << "Unsupported export format: #{options['format']}"
          return export_result
        end
        
        # Calculate export time
        export_result['exportTime'] = Time.now - start_time
        
        # Update statistics
        @export_stats['total_exports'] += 1
        if export_result['success']
          @export_stats['successful_exports'] += 1
          @export_stats['total_materials_exported'] += export_result['materialCount']
          @export_stats['total_textures_exported'] += export_result['textureCount']
        else
          @export_stats['failed_exports'] += 1
        end
        
        puts "Export completed: #{export_result['success'] ? 'SUCCESS' : 'FAILED'}"
        return export_result
        
      rescue => e
        puts "Error exporting materials: #{e.message}"
        return {
          'success' => false,
          'errors' => ["Export error: #{e.message}"]
        }
      end
    end
    
    # Import materials
    def self.import_materials(options_json)
      begin
        options = JSON.parse(options_json)
        puts "Importing materials with options: #{options}"
        
        # Prepare import result
        import_result = {
          'success' => false,
          'materialCount' => 0,
          'textureCount' => 0,
          'materials' => [],
          'importTime' => 0.0,
          'warnings' => [],
          'errors' => []
        }
        
        start_time = Time.now
        
        # Import each file
        options['files'].each do |file_path|
          file_result = import_material_file(file_path, options)
          
          if file_result['success']
            import_result['materials'].concat(file_result['materials'])
            import_result['materialCount'] += file_result['materialCount']
            import_result['textureCount'] += file_result['textureCount']
          else
            import_result['errors'].concat(file_result['errors'])
          end
        end
        
        # Determine overall success
        import_result['success'] = import_result['materialCount'] > 0
        
        # Calculate import time
        import_result['importTime'] = Time.now - start_time
        
        # Update statistics
        @import_stats['total_imports'] += 1
        if import_result['success']
          @import_stats['successful_imports'] += 1
          @import_stats['total_materials_imported'] += import_result['materialCount']
          @import_stats['total_textures_imported'] += import_result['textureCount']
        else
          @import_stats['failed_imports'] += 1
        end
        
        puts "Import completed: #{import_result['success'] ? 'SUCCESS' : 'FAILED'}"
        return import_result
        
      rescue => e
        puts "Error importing materials: #{e.message}"
        return {
          'success' => false,
          'errors' => ["Import error: #{e.message}"]
        }
      end
    end
    
    # Browse export path
    def self.browse_export_path(format)
      puts "Browse export path for format: #{format}"
      
      # Get file extension for format
      extension = get_format_extension(format)
      filter = get_format_filter(format)
      
      # Open save dialog
      file_path = UI.savepanel("Export Materials", @export_directory, "materials.#{extension}")
      
      if file_path
        puts "Selected export path: #{file_path}"
        return file_path
      else
        puts "Export path selection cancelled"
        return nil
      end
    end
    
    # Browse import files
    def self.browse_import_files
      puts "Browse import files"
      
      # Open file dialog for multiple files
      file_paths = UI.openpanel(
        "Import Materials", 
        @import_directory, 
        "Material Files|*.mtl;*.gltf;*.glb;*.json;*.obj||"
      )
      
      if file_paths
        # Handle both single file and multiple files
        files = file_paths.is_a?(Array) ? file_paths : [file_paths]
        puts "Selected import files: #{files}"
        return files
      else
        puts "Import file selection cancelled"
        return []
      end
    end
    
    # Get export statistics
    def self.get_export_stats
      @export_stats
    end
    
    # Get import statistics
    def self.get_import_stats
      @import_stats
    end
    
    private
    
    # Export to JSON format
    def self.export_to_json(materials, options)
      begin
        output_path = options['outputPath']
        
        # Create JSON structure
        json_data = {
          'format' => 'PhotonRender Materials',
          'version' => '1.0',
          'exportTime' => Time.now.to_i,
          'materials' => materials.map do |material|
            {
              'name' => material['name'],
              'id' => material['id'],
              'type' => 'DisneyBRDF',
              'parameters' => material['parameters'],
              'metadata' => {
                'exported' => Time.now.strftime('%Y-%m-%d %H:%M:%S'),
                'exporter' => 'PhotonRender SketchUp Plugin'
              }
            }
          end
        }
        
        # Write JSON file
        File.write(output_path, JSON.pretty_generate(json_data))
        
        {
          'success' => true,
          'materialCount' => materials.size,
          'textureCount' => 0,
          'exportedFiles' => [output_path]
        }
        
      rescue => e
        {
          'success' => false,
          'errors' => ["JSON export error: #{e.message}"]
        }
      end
    end
    
    # Export to MTL format
    def self.export_to_mtl(materials, options)
      begin
        output_path = options['outputPath']
        
        # Create MTL content
        mtl_content = "# PhotonRender MTL Export\n"
        mtl_content += "# Generated on #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        materials.each do |material|
          params = material['parameters']
          
          mtl_content += "newmtl #{material['name']}\n"
          mtl_content += "Ka #{params['baseColor'][0] * 0.1} #{params['baseColor'][1] * 0.1} #{params['baseColor'][2] * 0.1}\n"
          mtl_content += "Kd #{params['baseColor'][0]} #{params['baseColor'][1]} #{params['baseColor'][2]}\n"
          mtl_content += "Ks #{params['metallic']} #{params['metallic']} #{params['metallic']}\n"
          mtl_content += "Ns #{(1.0 - params['roughness']) * 1000}\n"
          mtl_content += "\n"
        end
        
        # Write MTL file
        File.write(output_path, mtl_content)
        
        {
          'success' => true,
          'materialCount' => materials.size,
          'textureCount' => 0,
          'exportedFiles' => [output_path]
        }
        
      rescue => e
        {
          'success' => false,
          'errors' => ["MTL export error: #{e.message}"]
        }
      end
    end
    
    # Export to glTF format
    def self.export_to_gltf(materials, options)
      begin
        output_path = options['outputPath']
        
        # Create glTF structure
        gltf_data = {
          'asset' => {
            'version' => '2.0',
            'generator' => 'PhotonRender SketchUp Plugin'
          },
          'materials' => materials.map do |material|
            params = material['parameters']
            {
              'name' => material['name'],
              'pbrMetallicRoughness' => {
                'baseColorFactor' => params['baseColor'] + [1.0],
                'metallicFactor' => params['metallic'],
                'roughnessFactor' => params['roughness']
              }
            }
          end
        }
        
        # Write glTF file
        File.write(output_path, JSON.pretty_generate(gltf_data))
        
        {
          'success' => true,
          'materialCount' => materials.size,
          'textureCount' => 0,
          'exportedFiles' => [output_path]
        }
        
      rescue => e
        {
          'success' => false,
          'errors' => ["glTF export error: #{e.message}"]
        }
      end
    end
    
    # Export to OBJ+MTL format
    def self.export_to_obj_mtl(materials, options)
      begin
        output_path = options['outputPath']
        mtl_path = output_path.gsub('.obj', '.mtl')
        
        # Export MTL first
        mtl_result = export_to_mtl(materials, options.merge('outputPath' => mtl_path))
        
        unless mtl_result['success']
          return mtl_result
        end
        
        # Create simple OBJ file
        obj_content = "# PhotonRender OBJ Export\n"
        obj_content += "mtllib #{File.basename(mtl_path)}\n\n"
        
        # Add simple geometry for each material (preview purposes)
        materials.each_with_index do |material, i|
          offset = i * 2.0
          
          obj_content += "# Material: #{material['name']}\n"
          obj_content += "v #{0.0 + offset} 0.0 0.0\n"
          obj_content += "v #{1.0 + offset} 0.0 0.0\n"
          obj_content += "v #{1.0 + offset} 1.0 0.0\n"
          obj_content += "v #{0.0 + offset} 1.0 0.0\n"
          
          obj_content += "vt 0.0 0.0\n"
          obj_content += "vt 1.0 0.0\n"
          obj_content += "vt 1.0 1.0\n"
          obj_content += "vt 0.0 1.0\n"
          
          obj_content += "usemtl #{material['name']}\n"
          
          base_vertex = i * 4 + 1
          obj_content += "f #{base_vertex}/#{base_vertex} #{base_vertex + 1}/#{base_vertex + 1} #{base_vertex + 2}/#{base_vertex + 2} #{base_vertex + 3}/#{base_vertex + 3}\n\n"
        end
        
        # Write OBJ file
        File.write(output_path, obj_content)
        
        {
          'success' => true,
          'materialCount' => materials.size,
          'textureCount' => 0,
          'exportedFiles' => [output_path, mtl_path]
        }
        
      rescue => e
        {
          'success' => false,
          'errors' => ["OBJ+MTL export error: #{e.message}"]
        }
      end
    end
    
    # Import material file
    def self.import_material_file(file_path, options)
      puts "Importing material file: #{file_path}"
      
      # Detect format if auto-detect is enabled
      format = options['format'] == 'auto' ? detect_file_format(file_path) : options['format']
      
      case format
      when 'json'
        import_from_json(file_path, options)
      when 'mtl'
        import_from_mtl(file_path, options)
      when 'gltf'
        import_from_gltf(file_path, options)
      when 'obj'
        import_from_obj(file_path, options)
      else
        {
          'success' => false,
          'errors' => ["Unsupported file format: #{format}"]
        }
      end
    end
    
    # Import from JSON format
    def self.import_from_json(file_path, options)
      begin
        json_data = JSON.parse(File.read(file_path))
        
        materials = json_data['materials'] || []
        imported_materials = []
        
        materials.each do |material_data|
          # Convert to internal format
          imported_material = {
            'name' => material_data['name'],
            'type' => 'PBR Material',
            'parameters' => material_data['parameters'] || {},
            'source' => 'JSON Import'
          }
          
          imported_materials << imported_material
        end
        
        {
          'success' => true,
          'materialCount' => imported_materials.size,
          'textureCount' => 0,
          'materials' => imported_materials
        }
        
      rescue => e
        {
          'success' => false,
          'errors' => ["JSON import error: #{e.message}"]
        }
      end
    end
    
    # Import from MTL format
    def self.import_from_mtl(file_path, options)
      begin
        mtl_content = File.read(file_path)
        imported_materials = []
        current_material = nil
        
        mtl_content.lines.each do |line|
          line = line.strip
          next if line.empty? || line.start_with?('#')
          
          parts = line.split
          command = parts[0]
          
          case command
          when 'newmtl'
            # Save previous material
            imported_materials << current_material if current_material
            
            # Start new material
            current_material = {
              'name' => parts[1],
              'type' => 'PBR Material',
              'parameters' => {
                'baseColor' => [0.8, 0.8, 0.8],
                'metallic' => 0.0,
                'roughness' => 0.5
              },
              'source' => 'MTL Import'
            }
            
          when 'Kd'
            # Diffuse color
            if current_material && parts.size >= 4
              current_material['parameters']['baseColor'] = [
                parts[1].to_f,
                parts[2].to_f,
                parts[3].to_f
              ]
            end
            
          when 'Ks'
            # Specular color (approximate metallic)
            if current_material && parts.size >= 4
              specular_avg = (parts[1].to_f + parts[2].to_f + parts[3].to_f) / 3.0
              current_material['parameters']['metallic'] = specular_avg
            end
            
          when 'Ns'
            # Specular exponent (convert to roughness)
            if current_material && parts.size >= 2
              ns = parts[1].to_f
              roughness = 1.0 - (ns / 1000.0).clamp(0.0, 1.0)
              current_material['parameters']['roughness'] = roughness
            end
          end
        end
        
        # Save last material
        imported_materials << current_material if current_material
        
        {
          'success' => true,
          'materialCount' => imported_materials.size,
          'textureCount' => 0,
          'materials' => imported_materials
        }
        
      rescue => e
        {
          'success' => false,
          'errors' => ["MTL import error: #{e.message}"]
        }
      end
    end
    
    # Import from glTF format
    def self.import_from_gltf(file_path, options)
      begin
        gltf_data = JSON.parse(File.read(file_path))
        
        materials = gltf_data['materials'] || []
        imported_materials = []
        
        materials.each do |material_data|
          pbr = material_data['pbrMetallicRoughness'] || {}
          
          imported_material = {
            'name' => material_data['name'] || 'Imported Material',
            'type' => 'PBR Material',
            'parameters' => {
              'baseColor' => (pbr['baseColorFactor'] || [1.0, 1.0, 1.0, 1.0])[0..2],
              'metallic' => pbr['metallicFactor'] || 0.0,
              'roughness' => pbr['roughnessFactor'] || 0.5
            },
            'source' => 'glTF Import'
          }
          
          imported_materials << imported_material
        end
        
        {
          'success' => true,
          'materialCount' => imported_materials.size,
          'textureCount' => 0,
          'materials' => imported_materials
        }
        
      rescue => e
        {
          'success' => false,
          'errors' => ["glTF import error: #{e.message}"]
        }
      end
    end
    
    # Import from OBJ format (look for associated MTL)
    def self.import_from_obj(file_path, options)
      begin
        obj_content = File.read(file_path)
        mtl_file = nil
        
        # Look for mtllib directive
        obj_content.lines.each do |line|
          if line.strip.start_with?('mtllib')
            mtl_filename = line.strip.split[1]
            mtl_path = File.join(File.dirname(file_path), mtl_filename)
            
            if File.exist?(mtl_path)
              mtl_file = mtl_path
              break
            end
          end
        end
        
        if mtl_file
          # Import from associated MTL file
          import_from_mtl(mtl_file, options)
        else
          {
            'success' => false,
            'errors' => ['No associated MTL file found for OBJ import']
          }
        end
        
      rescue => e
        {
          'success' => false,
          'errors' => ["OBJ import error: #{e.message}"]
        }
      end
    end
    
    # Detect file format from extension
    def self.detect_file_format(file_path)
      extension = File.extname(file_path).downcase
      
      case extension
      when '.json'
        'json'
      when '.mtl'
        'mtl'
      when '.gltf', '.glb'
        'gltf'
      when '.obj'
        'obj'
      else
        'unknown'
      end
    end
    
    # Get format extension
    def self.get_format_extension(format)
      case format
      when 'json'
        'json'
      when 'mtl'
        'mtl'
      when 'gltf'
        'gltf'
      when 'obj'
        'obj'
      else
        'txt'
      end
    end
    
    # Get format filter for file dialog
    def self.get_format_filter(format)
      case format
      when 'json'
        "JSON Files|*.json||"
      when 'mtl'
        "MTL Files|*.mtl||"
      when 'gltf'
        "glTF Files|*.gltf||"
      when 'obj'
        "OBJ Files|*.obj||"
      else
        "All Files|*.*||"
      end
    end
    
    # Setup export/import dialog callbacks
    def self.setup_export_import_callbacks(dialog)
      puts "Setting up Material Export/Import callbacks..."
      
      # Get materials for export callback
      dialog.add_action_callback("getMaterialsForExport") do |dialog|
        begin
          materials = get_materials_for_export
          
          js_code = "window.onMaterialsLoaded(#{materials.to_json});"
          dialog.execute_script(js_code)
          
        rescue => e
          puts "Error in getMaterialsForExport callback: #{e.message}"
        end
      end
      
      # Export materials callback
      dialog.add_action_callback("exportMaterials") do |dialog, options_json|
        begin
          # Start export in background thread to avoid blocking UI
          Thread.new do
            result = export_materials(options_json)
            
            # Update UI with result
            js_code = "window.onExportComplete(#{result.to_json});"
            dialog.execute_script(js_code)
          end
          
        rescue => e
          puts "Error in exportMaterials callback: #{e.message}"
          error_result = {
            'success' => false,
            'errors' => ["Export error: #{e.message}"]
          }
          js_code = "window.onExportComplete(#{error_result.to_json});"
          dialog.execute_script(js_code)
        end
      end
      
      # Import materials callback
      dialog.add_action_callback("importMaterials") do |dialog, options_json|
        begin
          # Start import in background thread to avoid blocking UI
          Thread.new do
            result = import_materials(options_json)
            
            # Update UI with result
            js_code = "window.onImportComplete(#{result.to_json});"
            dialog.execute_script(js_code)
          end
          
        rescue => e
          puts "Error in importMaterials callback: #{e.message}"
          error_result = {
            'success' => false,
            'errors' => ["Import error: #{e.message}"]
          }
          js_code = "window.onImportComplete(#{error_result.to_json});"
          dialog.execute_script(js_code)
        end
      end
      
      # Browse export path callback
      dialog.add_action_callback("browseExportPath") do |dialog, format|
        begin
          file_path = browse_export_path(format)
          
          if file_path
            js_code = "window.onExportPathSelected('#{file_path}');"
            dialog.execute_script(js_code)
          end
          
        rescue => e
          puts "Error in browseExportPath callback: #{e.message}"
        end
      end
      
      # Browse import files callback
      dialog.add_action_callback("browseImportFiles") do |dialog|
        begin
          file_paths = browse_import_files
          
          if file_paths && !file_paths.empty?
            js_code = "window.onImportFilesSelected(#{file_paths.to_json});"
            dialog.execute_script(js_code)
          end
          
        rescue => e
          puts "Error in browseImportFiles callback: #{e.message}"
        end
      end
      
      puts "Material Export/Import callbacks setup complete"
    end
    
    # Create fallback HTML if file not found
    def self.create_fallback_export_import_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Material Export/Import</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #e0e0e0; }
            .container { background: #2a2a2a; padding: 20px; border-radius: 8px; }
            h2 { color: #ffffff; margin-top: 0; }
            .error { text-align: center; padding: 50px; color: #ff6b6b; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Material Export/Import</h2>
            <div class="error">
              <h3>Export/Import Interface Loading Error</h3>
              <p>The material export/import interface file could not be loaded.</p>
              <p>Please ensure material_export_import.html is present in the plugin directory.</p>
            </div>
          </div>
        </body>
        </html>
      HTML
    end
    
  end
end
