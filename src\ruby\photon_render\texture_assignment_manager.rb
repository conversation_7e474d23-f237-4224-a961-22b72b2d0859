# src/ruby/photon_render/texture_assignment_manager.rb
# PhotonRender - Texture Assignment Manager Ruby Interface
# Sistema di gestione assegnazione texture per SketchUp

module PhotonRender
  module TextureAssignmentManager
    
    # Initialize texture assignment manager
    def self.initialize
      puts "Initializing PhotonRender Texture Assignment Manager..."
      
      # Set texture library path
      @texture_library_path = File.join(File.dirname(__FILE__), '..', '..', '..', 'assets', 'textures')
      
      # Create texture directory if it doesn't exist
      Dir.mkdir(@texture_library_path) unless Dir.exist?(@texture_library_path)
      
      # Initialize texture cache
      @texture_cache = {}
      @recent_textures = []
      @current_assignments = {}
      
      # Load existing textures
      load_texture_library
      
      puts "Texture Assignment Manager initialized successfully"
      puts "Texture library path: #{@texture_library_path}"
      puts "Textures loaded: #{@texture_cache.size}"
    end
    
    # Show texture assignment dialog
    def self.show_texture_assignment
      puts "Showing Texture Assignment interface..."
      
      # Load HTML content
      html_file = File.join(File.dirname(__FILE__), 'texture_assignment.html')
      
      if File.exist?(html_file)
        html_content = File.read(html_file)
      else
        puts "Warning: texture_assignment.html not found, using fallback"
        html_content = create_fallback_texture_assignment_html
      end
      
      # Create dialog
      dialog = UI::WebDialog.new("PhotonRender Texture Assignment", true, "photon_render_textures", 1200, 800, 50, 50, true)
      dialog.set_html(html_content)
      
      # Setup callbacks
      setup_texture_assignment_callbacks(dialog)
      
      dialog.show
      @texture_assignment_dialog = dialog
      
      puts "Texture Assignment interface opened successfully"
    end
    
    # Get all available textures
    def self.get_all_textures
      @texture_cache.values
    end
    
    # Load texture from file
    def self.load_texture(file_path)
      return nil unless File.exist?(file_path)
      
      begin
        # Generate texture ID
        texture_id = generate_texture_id(file_path)
        
        # Check if already loaded
        return @texture_cache[texture_id] if @texture_cache[texture_id]
        
        # Get file info
        file_stat = File.stat(file_path)
        file_ext = File.extname(file_path).downcase.gsub('.', '')
        
        # Create texture entry
        texture_entry = {
          'id' => texture_id,
          'name' => File.basename(file_path, '.*'),
          'file_path' => file_path,
          'format' => file_ext,
          'file_size' => file_stat.size,
          'created_date' => file_stat.ctime.strftime('%Y-%m-%d %H:%M:%S'),
          'modified_date' => file_stat.mtime.strftime('%Y-%m-%d %H:%M:%S'),
          'width' => 1024,  # TODO: Get actual dimensions
          'height' => 1024,
          'channels' => 3,
          'isHDR' => ['hdr', 'exr', 'pfm'].include?(file_ext),
          'isRecent' => true
        }
        
        # Add to cache
        @texture_cache[texture_id] = texture_entry
        
        # Update recent textures
        update_recent_textures(texture_id)
        
        # Generate thumbnail
        generate_texture_thumbnail(texture_id)
        
        puts "Loaded texture: #{texture_id} (#{file_path})"
        return texture_entry
        
      rescue => e
        puts "Error loading texture: #{e.message}"
        return nil
      end
    end
    
    # Assign texture to material slot
    def self.assign_texture(texture_type, texture_id, assignment_data)
      puts "Assigning texture #{texture_id} to #{texture_type}"
      
      texture = @texture_cache[texture_id]
      return false unless texture
      
      # Store assignment
      @current_assignments[texture_type] = {
        'texture_id' => texture_id,
        'texture' => texture,
        'uv_transform' => assignment_data['uvTransform'] || default_uv_transform,
        'wrap_mode' => assignment_data['wrapMode'] || 'repeat',
        'intensity' => assignment_data['intensity'] || 1.0,
        'enabled' => true
      }
      
      # Update recent textures
      update_recent_textures(texture_id)
      
      # TODO: Interface with C++ TextureManager
      puts "Texture assigned successfully: #{texture_type} -> #{texture_id}"
      return true
    end
    
    # Clear texture assignment
    def self.clear_texture_assignment(texture_type)
      puts "Clearing texture assignment: #{texture_type}"
      
      @current_assignments.delete(texture_type)
      
      # TODO: Interface with C++ TextureManager
      puts "Texture assignment cleared: #{texture_type}"
      return true
    end
    
    # Update UV transform
    def self.update_uv_transform(texture_type, transform_data)
      return false unless @current_assignments[texture_type]
      
      @current_assignments[texture_type]['uv_transform'] = transform_data
      
      # TODO: Interface with C++ TextureManager
      puts "UV transform updated: #{texture_type}"
      return true
    end
    
    # Update wrap mode
    def self.update_wrap_mode(texture_type, wrap_mode)
      return false unless @current_assignments[texture_type]
      
      @current_assignments[texture_type]['wrap_mode'] = wrap_mode
      
      # TODO: Interface with C++ TextureManager
      puts "Wrap mode updated: #{texture_type} -> #{wrap_mode}"
      return true
    end
    
    # Update texture intensity
    def self.update_texture_intensity(texture_type, intensity)
      return false unless @current_assignments[texture_type]
      
      @current_assignments[texture_type]['intensity'] = intensity
      
      # TODO: Interface with C++ TextureManager
      puts "Texture intensity updated: #{texture_type} -> #{intensity}"
      return true
    end
    
    # Apply texture assignments to material
    def self.apply_texture_assignments(assignments_data)
      puts "Applying texture assignments to material..."
      
      # Store assignments
      @current_assignments = assignments_data
      
      # TODO: Interface with C++ material system
      # Convert assignments to C++ format and apply
      
      puts "Texture assignments applied successfully"
      return true
    end
    
    # Preview material with textures
    def self.preview_material_with_textures(assignments_data)
      puts "Generating preview with textures..."
      
      # TODO: Interface with PreviewRenderer
      # Generate preview with texture assignments
      
      puts "Preview generated with textures"
      return true
    end
    
    # Get texture statistics
    def self.get_texture_stats
      stats = {
        'total_textures' => @texture_cache.size,
        'recent_textures' => @recent_textures.size,
        'current_assignments' => @current_assignments.size,
        'hdr_textures' => @texture_cache.count { |id, tex| tex['isHDR'] },
        'total_size' => @texture_cache.sum { |id, tex| tex['file_size'] || 0 }
      }
      
      stats
    end
    
    # Search textures
    def self.search_textures(query, filter = 'all')
      results = @texture_cache.values
      
      # Apply search query
      if query && !query.empty?
        query_lower = query.downcase
        results = results.select do |texture|
          texture['name'].downcase.include?(query_lower)
        end
      end
      
      # Apply filter
      case filter
      when 'recent'
        results = results.select { |tex| tex['isRecent'] }
      when 'hdr'
        results = results.select { |tex| tex['isHDR'] }
      when 'normal'
        results = results.select { |tex| tex['name'].downcase.include?('normal') }
      end
      
      results
    end
    
    # Export texture assignments
    def self.export_texture_assignments(file_path)
      begin
        export_data = {
          'version' => '1.0',
          'exported' => Time.now.strftime('%Y-%m-%d %H:%M:%S'),
          'assignments' => @current_assignments
        }
        
        File.write(file_path, JSON.pretty_generate(export_data))
        puts "Texture assignments exported to: #{file_path}"
        return true
        
      rescue => e
        puts "Error exporting texture assignments: #{e.message}"
        return false
      end
    end
    
    # Import texture assignments
    def self.import_texture_assignments(file_path)
      return false unless File.exist?(file_path)
      
      begin
        import_data = JSON.parse(File.read(file_path))
        @current_assignments = import_data['assignments'] || {}
        
        puts "Texture assignments imported from: #{file_path}"
        return true
        
      rescue => e
        puts "Error importing texture assignments: #{e.message}"
        return false
      end
    end
    
    private
    
    # Load texture library from disk
    def self.load_texture_library
      @texture_cache = {}
      @recent_textures = []
      
      return unless Dir.exist?(@texture_library_path)
      
      # Supported texture formats
      supported_formats = %w[png jpg jpeg bmp tga tiff tif hdr exr pfm]
      
      # Scan texture directory
      Dir.glob(File.join(@texture_library_path, "**", "*")).each do |file_path|
        next unless File.file?(file_path)
        
        file_ext = File.extname(file_path).downcase.gsub('.', '')
        next unless supported_formats.include?(file_ext)
        
        load_texture(file_path)
      end
      
      puts "Loaded #{@texture_cache.size} textures from library"
    end
    
    # Generate unique texture ID
    def self.generate_texture_id(file_path)
      # Generate ID based on file path and modification time
      file_stat = File.stat(file_path)
      "tex_#{Digest::MD5.hexdigest(file_path)}_#{file_stat.mtime.to_i}"
    end
    
    # Update recent textures list
    def self.update_recent_textures(texture_id)
      # Remove if already in list
      @recent_textures.delete(texture_id)
      
      # Add to front
      @recent_textures.unshift(texture_id)
      
      # Keep only last 20 recent textures
      @recent_textures = @recent_textures.first(20)
      
      # Update isRecent flag
      @texture_cache.each do |id, texture|
        texture['isRecent'] = @recent_textures.include?(id)
      end
    end
    
    # Generate texture thumbnail
    def self.generate_texture_thumbnail(texture_id)
      # TODO: Interface with C++ TextureManager to generate thumbnail
      # For now, just log
      puts "Generated thumbnail for texture: #{texture_id}"
    end
    
    # Default UV transform
    def self.default_uv_transform
      {
        'offsetU' => 0.0,
        'offsetV' => 0.0,
        'scaleU' => 1.0,
        'scaleV' => 1.0,
        'rotation' => 0.0
      }
    end
    
    # Setup texture assignment dialog callbacks
    def self.setup_texture_assignment_callbacks(dialog)
      puts "Setting up Texture Assignment callbacks..."
      
      # Get textures callback
      dialog.add_action_callback("getTextures") do |dialog|
        begin
          textures = get_all_textures
          
          js_code = "window.onTexturesLoaded(#{textures.to_json});"
          dialog.execute_script(js_code)
          
        rescue => e
          puts "Error in getTextures callback: #{e.message}"
        end
      end
      
      # Assign texture callback
      dialog.add_action_callback("assignTexture") do |dialog, params|
        begin
          data = JSON.parse(params)
          texture_type = data['textureType']
          texture_id = data['textureId']
          assignment = data['assignment']
          
          assign_texture(texture_type, texture_id, assignment)
          
        rescue => e
          puts "Error in assignTexture callback: #{e.message}"
        end
      end
      
      # Clear texture callback
      dialog.add_action_callback("clearTexture") do |dialog, texture_type|
        begin
          clear_texture_assignment(texture_type)
          
        rescue => e
          puts "Error in clearTexture callback: #{e.message}"
        end
      end
      
      # Browse texture callback
      dialog.add_action_callback("browseTexture") do |dialog, texture_type|
        begin
          # Open file dialog
          texture_path = UI.openpanel(
            "Select Texture", 
            @texture_library_path, 
            "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.tga;*.tiff;*.hdr;*.exr||"
          )
          
          if texture_path
            texture = load_texture(texture_path)
            if texture
              # Notify JavaScript
              js_code = "window.onTextureAssigned('#{texture_type}', '#{texture['id']}');"
              dialog.execute_script(js_code)
            end
          end
          
        rescue => e
          puts "Error in browseTexture callback: #{e.message}"
        end
      end
      
      # Load texture file callback
      dialog.add_action_callback("loadTextureFile") do |dialog, params|
        begin
          data = JSON.parse(params)
          texture_type = data['textureType']
          file_path = data['filePath']
          
          texture = load_texture(file_path)
          if texture
            # Notify JavaScript
            js_code = "window.onTextureAssigned('#{texture_type}', '#{texture['id']}');"
            dialog.execute_script(js_code)
          end
          
        rescue => e
          puts "Error in loadTextureFile callback: #{e.message}"
        end
      end
      
      # Update UV transform callback
      dialog.add_action_callback("updateUVTransform") do |dialog, params|
        begin
          data = JSON.parse(params)
          update_uv_transform(data['textureType'], data['transform'])
          
        rescue => e
          puts "Error in updateUVTransform callback: #{e.message}"
        end
      end
      
      # Update wrap mode callback
      dialog.add_action_callback("updateWrapMode") do |dialog, params|
        begin
          data = JSON.parse(params)
          update_wrap_mode(data['textureType'], data['wrapMode'])
          
        rescue => e
          puts "Error in updateWrapMode callback: #{e.message}"
        end
      end
      
      # Update texture intensity callback
      dialog.add_action_callback("updateTextureIntensity") do |dialog, params|
        begin
          data = JSON.parse(params)
          update_texture_intensity(data['textureType'], data['intensity'])
          
        rescue => e
          puts "Error in updateTextureIntensity callback: #{e.message}"
        end
      end
      
      # Apply texture assignments callback
      dialog.add_action_callback("applyTextureAssignments") do |dialog, params|
        begin
          assignments = JSON.parse(params)
          apply_texture_assignments(assignments)
          
          UI.messagebox("Texture assignments applied successfully!", MB_OK)
          
        rescue => e
          puts "Error in applyTextureAssignments callback: #{e.message}"
          UI.messagebox("Error applying texture assignments: #{e.message}", MB_OK)
        end
      end
      
      # Preview material with textures callback
      dialog.add_action_callback("previewMaterialWithTextures") do |dialog, params|
        begin
          assignments = JSON.parse(params)
          preview_material_with_textures(assignments)
          
        rescue => e
          puts "Error in previewMaterialWithTextures callback: #{e.message}"
        end
      end
      
      puts "Texture Assignment callbacks setup complete"
    end
    
    # Create fallback HTML if file not found
    def self.create_fallback_texture_assignment_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Texture Assignment</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #e0e0e0; }
            .container { background: #2a2a2a; padding: 20px; border-radius: 8px; }
            h2 { color: #ffffff; margin-top: 0; }
            .error { text-align: center; padding: 50px; color: #ff6b6b; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Texture Assignment</h2>
            <div class="error">
              <h3>Texture Assignment Loading Error</h3>
              <p>The texture assignment interface file could not be loaded.</p>
              <p>Please ensure texture_assignment.html is present in the plugin directory.</p>
            </div>
          </div>
        </body>
        </html>
      HTML
    end
    
  end
end
