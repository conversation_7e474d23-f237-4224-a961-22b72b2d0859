# src/ruby/photon_render/material_validation_manager.rb
# PhotonRender - Material Validation Manager Ruby Interface
# Sistema di gestione validazione materiali per SketchUp

module PhotonRender
  module MaterialValidationManager
    
    # Initialize material validation manager
    def self.initialize
      puts "Initializing PhotonRender Material Validation Manager..."
      
      # Initialize validation configuration
      @validation_config = {
        'enableEnergyConservation' => true,
        'enableParameterRange' => true,
        'enableParameterCombination' => true,
        'enableTextureCompatibility' => true,
        'enablePerformanceWarnings' => true,
        'enablePhysicalPlausibility' => true,
        'enableWorkflowSuggestions' => true,
        'energyConservationTolerance' => 0.05,
        'performanceThreshold' => 0.8,
        'strictMode' => false,
        'autoFix' => false
      }
      
      # Initialize validation statistics
      @validation_stats = {
        'total_validations' => 0,
        'total_issues' => 0,
        'auto_fixes_applied' => 0,
        'last_validation' => nil
      }
      
      # Current validation result
      @current_validation_result = nil
      
      puts "Material Validation Manager initialized successfully"
    end
    
    # Show material validation feedback dialog
    def self.show_validation_feedback
      puts "Showing Material Validation Feedback interface..."
      
      # Load HTML content
      html_file = File.join(File.dirname(__FILE__), 'material_validation_feedback.html')
      
      if File.exist?(html_file)
        html_content = File.read(html_file)
      else
        puts "Warning: material_validation_feedback.html not found, using fallback"
        html_content = create_fallback_validation_feedback_html
      end
      
      # Create dialog
      dialog = UI::WebDialog.new("PhotonRender Material Validation", true, "photon_render_validation", 1000, 700, 50, 50, true)
      dialog.set_html(html_content)
      
      # Setup callbacks
      setup_validation_feedback_callbacks(dialog)
      
      dialog.show
      @validation_feedback_dialog = dialog
      
      puts "Material Validation Feedback interface opened successfully"
    end
    
    # Validate current material
    def self.validate_current_material
      puts "Validating current material..."
      
      # TODO: Get current material from Material Editor
      # For now, use mock material parameters
      material_params = get_current_material_params
      
      # Perform validation
      validation_result = validate_material_params(material_params)
      
      # Store result
      @current_validation_result = validation_result
      
      # Update statistics
      @validation_stats['total_validations'] += 1
      @validation_stats['total_issues'] += validation_result['issues'].size
      @validation_stats['last_validation'] = Time.now.strftime('%Y-%m-%d %H:%M:%S')
      
      puts "Material validation completed: #{validation_result['issues'].size} issues found"
      return validation_result
    end
    
    # Validate material parameters
    def self.validate_material_params(params)
      puts "Validating material parameters: #{params.keys.join(', ')}"
      
      validation_result = {
        'isValid' => true,
        'hasErrors' => false,
        'hasWarnings' => false,
        'energyConservationScore' => 1.0,
        'physicalPlausibilityScore' => 1.0,
        'performanceScore' => 1.0,
        'issues' => []
      }
      
      # Parameter range validation
      if @validation_config['enableParameterRange']
        range_issues = validate_parameter_ranges(params)
        validation_result['issues'].concat(range_issues)
      end
      
      # Energy conservation validation
      if @validation_config['enableEnergyConservation']
        energy_issues = validate_energy_conservation(params)
        validation_result['issues'].concat(energy_issues)
        validation_result['energyConservationScore'] = calculate_energy_conservation_score(params)
      end
      
      # Parameter combination validation
      if @validation_config['enableParameterCombination']
        combination_issues = validate_parameter_combinations(params)
        validation_result['issues'].concat(combination_issues)
      end
      
      # Physical plausibility validation
      if @validation_config['enablePhysicalPlausibility']
        plausibility_issues = validate_physical_plausibility(params)
        validation_result['issues'].concat(plausibility_issues)
        validation_result['physicalPlausibilityScore'] = calculate_physical_plausibility_score(params)
      end
      
      # Performance validation
      if @validation_config['enablePerformanceWarnings']
        performance_issues = validate_performance(params)
        validation_result['issues'].concat(performance_issues)
        validation_result['performanceScore'] = calculate_performance_score(params)
      end
      
      # Workflow suggestions
      if @validation_config['enableWorkflowSuggestions']
        workflow_issues = get_workflow_suggestions(params)
        validation_result['issues'].concat(workflow_issues)
      end
      
      # Determine overall validity
      validation_result['hasErrors'] = validation_result['issues'].any? { |issue| ['ERROR', 'CRITICAL'].include?(issue['severity']) }
      validation_result['hasWarnings'] = validation_result['issues'].any? { |issue| issue['severity'] == 'WARNING' }
      validation_result['isValid'] = !validation_result['hasErrors']
      
      validation_result
    end
    
    # Auto-fix material issues
    def self.auto_fix_material_issues(issues_json)
      begin
        issues = JSON.parse(issues_json)
        puts "Auto-fixing #{issues.size} material issues..."
        
        # Get current material parameters
        params = get_current_material_params
        fixed_count = 0
        
        issues.each do |issue|
          next unless issue['autoFixable']
          
          parameter = issue['parameter']
          suggested_value = issue['suggestedValue']
          
          if params.key?(parameter) && suggested_value
            params[parameter] = suggested_value.clamp(0.0, 1.0)
            fixed_count += 1
            puts "Fixed #{parameter}: #{suggested_value}"
          end
        end
        
        # Apply fixed parameters
        if fixed_count > 0
          apply_material_params(params)
          @validation_stats['auto_fixes_applied'] += fixed_count
          
          # Re-validate after fixes
          validate_current_material
        end
        
        puts "Auto-fixed #{fixed_count} issues"
        return fixed_count
        
      rescue => e
        puts "Error auto-fixing issues: #{e.message}"
        return 0
      end
    end
    
    # Fix specific material parameter
    def self.fix_material_parameter(param_json)
      begin
        param_data = JSON.parse(param_json)
        parameter = param_data['parameter']
        value = param_data['value']
        
        puts "Fixing parameter: #{parameter} = #{value}"
        
        # Get current parameters
        params = get_current_material_params
        
        # Apply fix
        if params.key?(parameter)
          params[parameter] = value.clamp(0.0, 1.0)
          apply_material_params(params)
          
          puts "Parameter fixed successfully: #{parameter} = #{value}"
          return true
        else
          puts "Unknown parameter: #{parameter}"
          return false
        end
        
      rescue => e
        puts "Error fixing parameter: #{e.message}"
        return false
      end
    end
    
    # Get validation configuration
    def self.get_validation_config
      @validation_config
    end
    
    # Set validation configuration
    def self.set_validation_config(config)
      @validation_config.merge!(config)
      puts "Validation configuration updated"
    end
    
    # Get validation statistics
    def self.get_validation_stats
      @validation_stats
    end
    
    # Export validation report
    def self.export_validation_report(result_json)
      begin
        result = JSON.parse(result_json)
        
        # Generate report filename
        timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
        filename = "material_validation_report_#{timestamp}.txt"
        
        # Choose save location
        file_path = UI.savepanel("Save Validation Report", "", filename)
        return false unless file_path
        
        # Generate report content
        report_content = generate_validation_report(result)
        
        # Save report
        File.write(file_path, report_content)
        
        puts "Validation report exported to: #{file_path}"
        UI.messagebox("Validation report exported successfully!", MB_OK)
        return true
        
      rescue => e
        puts "Error exporting validation report: #{e.message}"
        UI.messagebox("Error exporting report: #{e.message}", MB_OK)
        return false
      end
    end
    
    private
    
    # Get current material parameters (mock implementation)
    def self.get_current_material_params
      # TODO: Interface with actual Material Editor
      # For now, return mock parameters
      {
        'baseColor' => [0.8, 0.2, 0.2],
        'metallic' => 0.0,
        'roughness' => 0.5,
        'specular' => 0.5,
        'specularTint' => 0.0,
        'anisotropic' => 0.0,
        'sheen' => 0.0,
        'sheenTint' => 0.5,
        'clearcoat' => 0.0,
        'clearcoatGloss' => 1.0,
        'subsurface' => 0.0
      }
    end
    
    # Apply material parameters (mock implementation)
    def self.apply_material_params(params)
      # TODO: Interface with actual Material Editor
      puts "Applied material parameters: #{params}"
    end
    
    # Validate parameter ranges
    def self.validate_parameter_ranges(params)
      issues = []
      
      params.each do |param, value|
        next if param == 'baseColor' # Skip color parameters
        
        if value < 0.0
          issues << create_issue('ERROR', 'PARAMETER_RANGE', param, 
                                "Parameter value below minimum range", 
                                "Increase value to valid range", value, 0.0, true)
        elsif value > 1.0
          issues << create_issue('ERROR', 'PARAMETER_RANGE', param, 
                                "Parameter value above maximum range", 
                                "Decrease value to valid range", value, 1.0, true)
        end
        
        # Special case for roughness
        if param == 'roughness' && value < 0.01
          issues << create_issue('WARNING', 'PHYSICAL_PLAUSIBILITY', param, 
                                "Very low roughness may cause rendering artifacts", 
                                "Consider using roughness >= 0.01 for stability", value, 0.01, true)
        end
      end
      
      issues
    end
    
    # Validate energy conservation
    def self.validate_energy_conservation(params)
      issues = []
      
      metallic = params['metallic'] || 0.0
      specular = params['specular'] || 0.5
      subsurface = params['subsurface'] || 0.0
      clearcoat = params['clearcoat'] || 0.0
      
      # Check diffuse + specular energy conservation
      diffuse_energy = (1.0 - metallic) * (1.0 - subsurface)
      specular_energy = specular * (1.0 - metallic) + metallic
      total_energy = diffuse_energy + specular_energy
      
      tolerance = @validation_config['energyConservationTolerance']
      
      if total_energy > 1.0 + tolerance
        issues << create_issue('WARNING', 'ENERGY_CONSERVATION', 'energy_total', 
                              "Material may violate energy conservation", 
                              "Reduce metallic or specular values", total_energy, 1.0, true)
      end
      
      # Check clearcoat energy conservation
      if clearcoat > 0.0
        clearcoat_energy = clearcoat * 0.25 # Approximate clearcoat energy
        if total_energy + clearcoat_energy > 1.0 + tolerance
          issues << create_issue('WARNING', 'ENERGY_CONSERVATION', 'clearcoat', 
                                "Clearcoat may cause energy conservation violation", 
                                "Reduce clearcoat or other reflective parameters", 
                                clearcoat, clearcoat * 0.8, true)
        end
      end
      
      issues
    end
    
    # Validate parameter combinations
    def self.validate_parameter_combinations(params)
      issues = []
      
      metallic = params['metallic'] || 0.0
      specular = params['specular'] || 0.5
      subsurface = params['subsurface'] || 0.0
      clearcoat = params['clearcoat'] || 0.0
      clearcoat_gloss = params['clearcoatGloss'] || 1.0
      
      # Metallic + Subsurface incompatibility
      if subsurface > 0.1 && metallic > 0.1
        issues << create_issue('WARNING', 'PARAMETER_COMBINATION', 'subsurface+metallic', 
                              "Subsurface scattering with metallic materials is physically implausible", 
                              "Set either subsurface or metallic to 0", subsurface, 0.0, true)
      end
      
      # Metallic + Specular combination
      if metallic > 0.8 && specular < 0.5
        issues << create_issue('INFO', 'PARAMETER_COMBINATION', 'metallic+specular', 
                              "High metallic with low specular may look unrealistic", 
                              "Consider increasing specular for metallic materials", specular, 0.9, false)
      end
      
      # Clearcoat validation
      if clearcoat > 0.1 && clearcoat_gloss < 0.1
        issues << create_issue('WARNING', 'PARAMETER_COMBINATION', 'clearcoat+clearcoatGloss', 
                              "Clearcoat without proper gloss setting", 
                              "Adjust clearcoat gloss for realistic appearance", clearcoat_gloss, 0.9, true)
      end
      
      issues
    end
    
    # Validate physical plausibility
    def self.validate_physical_plausibility(params)
      issues = []
      
      roughness = params['roughness'] || 0.5
      specular = params['specular'] || 0.5
      anisotropic = params['anisotropic'] || 0.0
      
      # Very high roughness with high specular
      if roughness > 0.9 && specular > 0.8
        issues << create_issue('INFO', 'PHYSICAL_PLAUSIBILITY', 'roughness+specular', 
                              "Very rough surfaces typically have lower specular reflection", 
                              "Consider reducing specular for very rough materials", specular, 0.3, false)
      end
      
      # Anisotropic without proper setup
      if anisotropic > 0.5 && roughness < 0.1
        issues << create_issue('INFO', 'PHYSICAL_PLAUSIBILITY', 'anisotropic+roughness', 
                              "Anisotropic effects are more visible with moderate roughness", 
                              "Increase roughness for better anisotropic visibility", roughness, 0.3, false)
      end
      
      issues
    end
    
    # Validate performance
    def self.validate_performance(params)
      issues = []
      
      subsurface = params['subsurface'] || 0.0
      clearcoat = params['clearcoat'] || 0.0
      
      complexity = estimate_rendering_complexity(params)
      threshold = @validation_config['performanceThreshold']
      
      if complexity > threshold
        issues << create_issue('INFO', 'PERFORMANCE', 'complexity', 
                              "Material has high rendering complexity", 
                              "Consider simplifying for better performance", complexity, threshold, false)
      end
      
      # Subsurface scattering performance warning
      if subsurface > 0.1
        issues << create_issue('INFO', 'PERFORMANCE', 'subsurface', 
                              "Subsurface scattering increases render time", 
                              "Use sparingly for performance-critical scenes", subsurface, 0.0, false)
      end
      
      # Clearcoat performance warning
      if clearcoat > 0.1
        issues << create_issue('INFO', 'PERFORMANCE', 'clearcoat', 
                              "Clearcoat adds additional reflection layer", 
                              "Consider if clearcoat is necessary for the material", clearcoat, 0.0, false)
      end
      
      issues
    end
    
    # Get workflow suggestions
    def self.get_workflow_suggestions(params)
      issues = []
      
      metallic = params['metallic'] || 0.0
      roughness = params['roughness'] || 0.5
      subsurface = params['subsurface'] || 0.0
      
      # Suggest using presets for common materials
      if metallic > 0.9 && roughness < 0.1
        issues << create_issue('INFO', 'WORKFLOW', 'preset_suggestion', 
                              "This looks like a chrome/mirror material", 
                              "Consider using the Chrome preset as starting point", 0.0, 0.0, false)
      end
      
      if subsurface > 0.3 && metallic < 0.1
        issues << create_issue('INFO', 'WORKFLOW', 'preset_suggestion', 
                              "This looks like a skin/wax material", 
                              "Consider using the Skin preset as starting point", 0.0, 0.0, false)
      end
      
      # Suggest texture usage
      if roughness == 0.5 && metallic == 0.0
        issues << create_issue('INFO', 'WORKFLOW', 'texture_suggestion', 
                              "Consider adding texture maps for more realism", 
                              "Add roughness and normal maps for surface detail", 0.0, 0.0, false)
      end
      
      issues
    end
    
    # Calculate energy conservation score
    def self.calculate_energy_conservation_score(params)
      metallic = params['metallic'] || 0.0
      specular = params['specular'] || 0.5
      subsurface = params['subsurface'] || 0.0
      clearcoat = params['clearcoat'] || 0.0
      
      diffuse_energy = (1.0 - metallic) * (1.0 - subsurface)
      specular_energy = specular * (1.0 - metallic) + metallic
      clearcoat_energy = clearcoat * 0.25
      total_energy = diffuse_energy + specular_energy + clearcoat_energy
      
      if total_energy <= 1.0
        1.0 # Perfect conservation
      else
        # Penalize energy violations
        violation = total_energy - 1.0
        [0.0, 1.0 - violation * 2.0].max
      end
    end
    
    # Calculate physical plausibility score
    def self.calculate_physical_plausibility_score(params)
      score = 1.0
      
      metallic = params['metallic'] || 0.0
      roughness = params['roughness'] || 0.5
      specular = params['specular'] || 0.5
      subsurface = params['subsurface'] || 0.0
      
      # Penalize implausible combinations
      score -= 0.3 if subsurface > 0.1 && metallic > 0.1 # Subsurface + metallic is implausible
      score -= 0.2 if roughness < 0.01 # Perfect mirrors are rare
      score -= 0.1 if roughness > 0.9 && specular > 0.8 # Very rough + high specular is unusual
      
      [0.0, score].max
    end
    
    # Calculate performance score
    def self.calculate_performance_score(params)
      complexity = estimate_rendering_complexity(params)
      [0.0, 1.0 - complexity].max
    end
    
    # Estimate rendering complexity
    def self.estimate_rendering_complexity(params)
      complexity = 0.1 # Base complexity
      
      metallic = params['metallic'] || 0.0
      roughness = params['roughness'] || 0.5
      subsurface = params['subsurface'] || 0.0
      clearcoat = params['clearcoat'] || 0.0
      anisotropic = params['anisotropic'] || 0.0
      sheen = params['sheen'] || 0.0
      
      # Add complexity for each active feature
      complexity += 0.1 if metallic > 0.1
      complexity += 0.3 if subsurface > 0.1 # Subsurface is expensive
      complexity += 0.2 if clearcoat > 0.1
      complexity += 0.1 if anisotropic > 0.1
      complexity += 0.1 if sheen > 0.1
      
      # Very low roughness increases complexity (more bounces needed)
      complexity += 0.1 if roughness < 0.1
      
      [1.0, complexity].min
    end
    
    # Create validation issue
    def self.create_issue(severity, category, parameter, message, suggestion = '', current_value = 0.0, suggested_value = 0.0, auto_fixable = false)
      {
        'severity' => severity,
        'category' => category,
        'parameter' => parameter,
        'message' => message,
        'suggestion' => suggestion,
        'currentValue' => current_value,
        'suggestedValue' => suggested_value,
        'autoFixable' => auto_fixable
      }
    end
    
    # Generate validation report
    def self.generate_validation_report(result)
      report = "PhotonRender Material Validation Report\n"
      report += "=====================================\n\n"
      
      report += "Validation Date: #{Time.now.strftime('%Y-%m-%d %H:%M:%S')}\n"
      report += "Overall Status: #{result['isValid'] ? 'VALID' : 'INVALID'}\n"
      report += "Total Issues: #{result['issues'].size}\n\n"
      
      report += "Scores:\n"
      report += "Energy Conservation: #{(result['energyConservationScore'] * 100).round}%\n"
      report += "Physical Plausibility: #{(result['physicalPlausibilityScore'] * 100).round}%\n"
      report += "Performance: #{(result['performanceScore'] * 100).round}%\n\n"
      
      if result['issues'].size > 0
        report += "Issues Found:\n"
        report += "=============\n\n"
        
        result['issues'].each_with_index do |issue, index|
          report += "#{index + 1}. [#{issue['severity']}] #{issue['message']}\n"
          report += "   Parameter: #{issue['parameter']}\n" if issue['parameter']
          report += "   Suggestion: #{issue['suggestion']}\n" if issue['suggestion']
          report += "   Current Value: #{issue['currentValue']}\n" if issue['currentValue']
          report += "   Suggested Value: #{issue['suggestedValue']}\n" if issue['suggestedValue']
          report += "\n"
        end
      end
      
      report
    end
    
    # Setup validation feedback dialog callbacks
    def self.setup_validation_feedback_callbacks(dialog)
      puts "Setting up Material Validation Feedback callbacks..."
      
      # Get validation result callback
      dialog.add_action_callback("getValidationResult") do |dialog|
        begin
          result = validate_current_material
          
          js_code = "window.onValidationResult(#{result.to_json});"
          dialog.execute_script(js_code)
          
        rescue => e
          puts "Error in getValidationResult callback: #{e.message}"
        end
      end
      
      # Validate current material callback
      dialog.add_action_callback("validateCurrentMaterial") do |dialog|
        begin
          result = validate_current_material
          
          js_code = "window.onValidationResult(#{result.to_json});"
          dialog.execute_script(js_code)
          
        rescue => e
          puts "Error in validateCurrentMaterial callback: #{e.message}"
        end
      end
      
      # Auto-fix material issues callback
      dialog.add_action_callback("autoFixMaterialIssues") do |dialog, issues_json|
        begin
          fixed_count = auto_fix_material_issues(issues_json)
          
          if fixed_count > 0
            UI.messagebox("Auto-fixed #{fixed_count} issues successfully!", MB_OK)
            
            # Re-validate and update display
            result = validate_current_material
            js_code = "window.onValidationResult(#{result.to_json});"
            dialog.execute_script(js_code)
          else
            UI.messagebox("No issues were auto-fixed.", MB_OK)
          end
          
        rescue => e
          puts "Error in autoFixMaterialIssues callback: #{e.message}"
          UI.messagebox("Error auto-fixing issues: #{e.message}", MB_OK)
        end
      end
      
      # Fix material parameter callback
      dialog.add_action_callback("fixMaterialParameter") do |dialog, param_json|
        begin
          success = fix_material_parameter(param_json)
          
          if success
            # Notify JavaScript
            param_data = JSON.parse(param_json)
            js_code = "window.onParameterFixed('#{param_data['parameter']}', #{param_data['value']});"
            dialog.execute_script(js_code)
          end
          
        rescue => e
          puts "Error in fixMaterialParameter callback: #{e.message}"
        end
      end
      
      # Highlight parameter callback
      dialog.add_action_callback("highlightParameter") do |dialog, parameter|
        begin
          puts "Highlighting parameter: #{parameter}"
          # TODO: Interface with Material Editor to highlight parameter
          
        rescue => e
          puts "Error in highlightParameter callback: #{e.message}"
        end
      end
      
      # Open help URL callback
      dialog.add_action_callback("openHelpUrl") do |dialog, url|
        begin
          UI.openURL(url)
          
        rescue => e
          puts "Error in openHelpUrl callback: #{e.message}"
        end
      end
      
      # Export validation report callback
      dialog.add_action_callback("exportValidationReport") do |dialog, result_json|
        begin
          export_validation_report(result_json)
          
        rescue => e
          puts "Error in exportValidationReport callback: #{e.message}"
        end
      end
      
      puts "Material Validation Feedback callbacks setup complete"
    end
    
    # Create fallback HTML if file not found
    def self.create_fallback_validation_feedback_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Material Validation</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #e0e0e0; }
            .container { background: #2a2a2a; padding: 20px; border-radius: 8px; }
            h2 { color: #ffffff; margin-top: 0; }
            .error { text-align: center; padding: 50px; color: #ff6b6b; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Material Validation</h2>
            <div class="error">
              <h3>Material Validation Loading Error</h3>
              <p>The material validation interface file could not be loaded.</p>
              <p>Please ensure material_validation_feedback.html is present in the plugin directory.</p>
            </div>
          </div>
        </body>
        </html>
      HTML
    end
    
  end
end
