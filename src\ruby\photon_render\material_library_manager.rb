# src/ruby/photon_render/material_library_manager.rb
# PhotonRender - Material Library Manager Ruby Interface
# Sistema di gestione libreria materiali per SketchUp

module PhotonRender
  module MaterialLibraryManager
    
    # Initialize material library manager
    def self.initialize
      puts "Initializing PhotonRender Material Library Manager..."
      
      # Set library path
      @library_path = File.join(File.dirname(__FILE__), '..', '..', '..', 'assets', 'materials')
      
      # Create library directory if it doesn't exist
      Dir.mkdir(@library_path) unless Dir.exist?(@library_path)
      
      # Initialize categories
      @categories = {
        'metals' => 'Metal Materials',
        'plastics' => 'Plastic Materials', 
        'glass' => 'Glass Materials',
        'wood' => 'Wood Materials',
        'fabric' => 'Fabric Materials',
        'stone' => 'Stone Materials',
        'organic' => 'Organic Materials',
        'automotive' => 'Automotive Materials',
        'architectural' => 'Architectural Materials',
        'custom' => 'Custom Materials'
      }
      
      # Initialize tags
      @tags = {
        'rough' => 'Rough Surface',
        'smooth' => 'Smooth Surface',
        'reflective' => 'Reflective',
        'translucent' => 'Translucent',
        'emissive' => 'Emissive',
        'weathered' => 'Weathered',
        'polished' => 'Polished',
        'matte' => 'Matte Finish',
        'glossy' => 'Glossy Finish',
        'textured' => 'Textured Surface'
      }
      
      # Load existing library
      load_library_index
      
      puts "Material Library Manager initialized successfully"
      puts "Library path: #{@library_path}"
      puts "Materials loaded: #{@materials.size}"
    end
    
    # Show material library browser dialog
    def self.show_library_browser
      puts "Showing Material Library Browser..."
      
      # Load HTML content
      html_file = File.join(File.dirname(__FILE__), 'material_library_browser.html')
      
      if File.exist?(html_file)
        html_content = File.read(html_file)
      else
        puts "Warning: material_library_browser.html not found, using fallback"
        html_content = create_fallback_library_browser_html
      end
      
      # Create dialog
      dialog = UI::WebDialog.new("PhotonRender Material Library", true, "photon_render_library", 1400, 900, 50, 50, true)
      dialog.set_html(html_content)
      
      # Setup callbacks
      setup_library_browser_callbacks(dialog)
      
      dialog.show
      @library_browser_dialog = dialog
      
      puts "Material Library Browser opened successfully"
    end
    
    # Get all materials
    def self.get_all_materials
      @materials || {}
    end
    
    # Get materials by category
    def self.get_materials_by_category(category)
      return {} unless @materials
      
      @materials.select { |id, material| material['category'] == category }
    end
    
    # Search materials
    def self.search_materials(query, category = 'all', tags = [])
      return {} unless @materials
      
      results = @materials.dup
      
      # Filter by category
      if category != 'all'
        results = results.select { |id, material| material['category'] == category }
      end
      
      # Filter by search query
      if query && !query.empty?
        query_lower = query.downcase
        results = results.select do |id, material|
          material['name'].downcase.include?(query_lower) ||
          material['description'].downcase.include?(query_lower)
        end
      end
      
      # Filter by tags
      if tags && !tags.empty?
        results = results.select do |id, material|
          material_tags = material['tags'] || []
          tags.all? { |tag| material_tags.include?(tag) }
        end
      end
      
      results
    end
    
    # Add material to library
    def self.add_material(material_data)
      puts "Adding material to library: #{material_data['name']}"
      
      # Generate unique ID
      material_id = generate_material_id
      
      # Create material entry
      material_entry = {
        'id' => material_id,
        'name' => material_data['name'] || "Material #{material_id}",
        'description' => material_data['description'] || '',
        'category' => material_data['category'] || 'custom',
        'tags' => material_data['tags'] || [],
        'parameters' => material_data['parameters'] || {},
        'rating' => 0.0,
        'usage_count' => 0,
        'created_date' => Time.now.strftime('%Y-%m-%d %H:%M:%S'),
        'modified_date' => Time.now.strftime('%Y-%m-%d %H:%M:%S'),
        'author' => material_data['author'] || 'User',
        'version' => material_data['version'] || '1.0'
      }
      
      # Save material file
      material_file = File.join(@library_path, "#{material_id}.json")
      File.write(material_file, JSON.pretty_generate(material_entry))
      
      # Add to memory
      @materials ||= {}
      @materials[material_id] = material_entry
      
      # Generate thumbnail
      generate_material_thumbnail(material_id)
      
      # Save library index
      save_library_index
      
      puts "Material added successfully: #{material_id}"
      return material_id
    end
    
    # Remove material from library
    def self.remove_material(material_id)
      puts "Removing material from library: #{material_id}"
      
      return false unless @materials && @materials[material_id]
      
      # Remove files
      material_file = File.join(@library_path, "#{material_id}.json")
      thumbnail_file = File.join(@library_path, "thumbnails", "#{material_id}.png")
      
      File.delete(material_file) if File.exist?(material_file)
      File.delete(thumbnail_file) if File.exist?(thumbnail_file)
      
      # Remove from memory
      @materials.delete(material_id)
      
      # Save library index
      save_library_index
      
      puts "Material removed successfully: #{material_id}"
      return true
    end
    
    # Get material by ID
    def self.get_material(material_id)
      return nil unless @materials
      @materials[material_id]
    end
    
    # Update material rating
    def self.update_material_rating(material_id, rating)
      return false unless @materials && @materials[material_id]
      
      @materials[material_id]['rating'] = [0.0, [5.0, rating.to_f].min].max
      @materials[material_id]['modified_date'] = Time.now.strftime('%Y-%m-%d %H:%M:%S')
      
      # Save material file
      material_file = File.join(@library_path, "#{material_id}.json")
      File.write(material_file, JSON.pretty_generate(@materials[material_id]))
      
      save_library_index
      return true
    end
    
    # Increment material usage count
    def self.increment_usage_count(material_id)
      return false unless @materials && @materials[material_id]
      
      @materials[material_id]['usage_count'] = (@materials[material_id]['usage_count'] || 0) + 1
      @materials[material_id]['modified_date'] = Time.now.strftime('%Y-%m-%d %H:%M:%S')
      
      # Save material file
      material_file = File.join(@library_path, "#{material_id}.json")
      File.write(material_file, JSON.pretty_generate(@materials[material_id]))
      
      save_library_index
      return true
    end
    
    # Get library statistics
    def self.get_library_stats
      return {} unless @materials
      
      stats = {
        'total_materials' => @materials.size,
        'categories' => {},
        'total_usage' => 0,
        'average_rating' => 0.0
      }
      
      # Count by category
      @categories.keys.each do |category|
        count = @materials.count { |id, material| material['category'] == category }
        stats['categories'][category] = count if count > 0
      end
      
      # Calculate totals
      total_usage = @materials.sum { |id, material| material['usage_count'] || 0 }
      total_rating = @materials.sum { |id, material| material['rating'] || 0.0 }
      
      stats['total_usage'] = total_usage
      stats['average_rating'] = @materials.size > 0 ? (total_rating / @materials.size).round(2) : 0.0
      
      stats
    end
    
    # Export material to file
    def self.export_material(material_id, file_path)
      material = get_material(material_id)
      return false unless material
      
      begin
        File.write(file_path, JSON.pretty_generate(material))
        puts "Material exported to: #{file_path}"
        return true
      rescue => e
        puts "Error exporting material: #{e.message}"
        return false
      end
    end
    
    # Import material from file
    def self.import_material(file_path)
      return nil unless File.exist?(file_path)
      
      begin
        material_data = JSON.parse(File.read(file_path))
        
        # Remove ID to generate new one
        material_data.delete('id')
        
        # Add imported material
        material_id = add_material(material_data)
        
        puts "Material imported successfully: #{material_id}"
        return material_id
        
      rescue => e
        puts "Error importing material: #{e.message}"
        return nil
      end
    end
    
    # Get available categories
    def self.get_categories
      @categories
    end
    
    # Get available tags
    def self.get_tags
      @tags
    end
    
    private
    
    # Load library index from disk
    def self.load_library_index
      @materials = {}
      
      return unless Dir.exist?(@library_path)
      
      # Load all material files
      Dir.glob(File.join(@library_path, "*.json")).each do |file_path|
        begin
          material_data = JSON.parse(File.read(file_path))
          material_id = material_data['id'] || File.basename(file_path, '.json')
          @materials[material_id] = material_data
        rescue => e
          puts "Error loading material file #{file_path}: #{e.message}"
        end
      end
      
      puts "Loaded #{@materials.size} materials from library"
    end
    
    # Save library index to disk
    def self.save_library_index
      return unless @materials
      
      index_file = File.join(@library_path, 'library_index.json')
      
      index_data = {
        'version' => '1.0',
        'updated' => Time.now.strftime('%Y-%m-%d %H:%M:%S'),
        'materials' => @materials.keys,
        'categories' => @categories,
        'tags' => @tags
      }
      
      File.write(index_file, JSON.pretty_generate(index_data))
    end
    
    # Generate unique material ID
    def self.generate_material_id
      "mat_#{Time.now.to_i}_#{rand(1000..9999)}"
    end
    
    # Generate material thumbnail
    def self.generate_material_thumbnail(material_id)
      # TODO: Interface with C++ PreviewRenderer to generate thumbnail
      # For now, create placeholder
      
      thumbnails_dir = File.join(@library_path, 'thumbnails')
      Dir.mkdir(thumbnails_dir) unless Dir.exist?(thumbnails_dir)
      
      puts "Generated thumbnail for material: #{material_id}"
    end
    
    # Setup library browser dialog callbacks
    def self.setup_library_browser_callbacks(dialog)
      puts "Setting up Material Library Browser callbacks..."
      
      # Get materials callback
      dialog.add_action_callback("getMaterials") do |dialog, params|
        begin
          criteria = JSON.parse(params)
          materials = search_materials(
            criteria['query'], 
            criteria['category'], 
            criteria['tags']
          )
          
          # Convert to array format for JavaScript
          materials_array = materials.map do |id, material|
            material.merge('id' => id)
          end
          
          js_code = "window.libraryBrowser.onMaterialsLoaded(#{materials_array.to_json});"
          dialog.execute_script(js_code)
          
        rescue => e
          puts "Error in getMaterials callback: #{e.message}"
        end
      end
      
      # Apply material callback
      dialog.add_action_callback("applyMaterial") do |dialog, material_id|
        begin
          material = get_material(material_id)
          if material
            # Apply material to selection
            apply_material_to_selection(material)
            increment_usage_count(material_id)
            
            UI.messagebox("Material applied successfully!", MB_OK)
          else
            UI.messagebox("Material not found!", MB_OK)
          end
          
        rescue => e
          puts "Error in applyMaterial callback: #{e.message}"
          UI.messagebox("Error applying material: #{e.message}", MB_OK)
        end
      end
      
      # Delete material callback
      dialog.add_action_callback("deleteMaterial") do |dialog, material_id|
        begin
          if remove_material(material_id)
            UI.messagebox("Material deleted successfully!", MB_OK)
            
            # Refresh materials list
            js_code = "window.libraryBrowser.refreshMaterials();"
            dialog.execute_script(js_code)
          else
            UI.messagebox("Failed to delete material!", MB_OK)
          end
          
        rescue => e
          puts "Error in deleteMaterial callback: #{e.message}"
          UI.messagebox("Error deleting material: #{e.message}", MB_OK)
        end
      end
      
      # Rate material callback
      dialog.add_action_callback("rateMaterial") do |dialog, params|
        begin
          data = JSON.parse(params)
          material_id = data['materialId']
          rating = data['rating'].to_f
          
          if update_material_rating(material_id, rating)
            puts "Material rating updated: #{material_id} -> #{rating}"
          end
          
        rescue => e
          puts "Error in rateMaterial callback: #{e.message}"
        end
      end
      
      puts "Material Library Browser callbacks setup complete"
    end
    
    # Apply material to selected entities
    def self.apply_material_to_selection(material_data)
      model = Sketchup.active_model
      selection = model.selection
      
      if selection.empty?
        UI.messagebox("Please select some entities to apply the material to.", MB_OK)
        return
      end
      
      # Create SketchUp material from PhotonRender material
      materials = model.materials
      material_name = "PhotonRender_#{material_data['name']}"
      
      # Remove existing material with same name
      existing_material = materials[material_name]
      materials.remove(existing_material) if existing_material
      
      # Create new material
      material = materials.add(material_name)
      
      # Set basic properties from parameters
      params = material_data['parameters'] || {}
      base_color = params['baseColor'] || [0.8, 0.2, 0.2]
      
      color = Sketchup::Color.new(
        (base_color[0] * 255).to_i,
        (base_color[1] * 255).to_i,
        (base_color[2] * 255).to_i
      )
      material.color = color
      
      # Set alpha based on material properties
      alpha = 1.0
      if params['subsurface'] && params['subsurface'] > 0
        alpha = 0.9
      end
      material.alpha = alpha
      
      # Apply material to selection
      model.start_operation('Apply PhotonRender Material')
      
      selection.each do |entity|
        if entity.respond_to?(:material=)
          entity.material = material
        elsif entity.is_a?(Sketchup::Face)
          entity.material = material
        end
      end
      
      model.commit_operation
      
      puts "Applied material '#{material_data['name']}' to #{selection.length} entities"
    end
    
    # Create fallback HTML if file not found
    def self.create_fallback_library_browser_html
      <<~HTML
        <!DOCTYPE html>
        <html>
        <head>
          <title>PhotonRender Material Library</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: #e0e0e0; }
            .container { background: #2a2a2a; padding: 20px; border-radius: 8px; }
            h2 { color: #ffffff; margin-top: 0; }
            .error { text-align: center; padding: 50px; color: #ff6b6b; }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>PhotonRender Material Library</h2>
            <div class="error">
              <h3>Material Library Loading Error</h3>
              <p>The material library interface file could not be loaded.</p>
              <p>Please ensure material_library_browser.html is present in the plugin directory.</p>
            </div>
          </div>
        </body>
        </html>
      HTML
    end
    
  end
end
