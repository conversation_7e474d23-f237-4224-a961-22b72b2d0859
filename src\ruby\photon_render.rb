# src/ruby/photon_render.rb
# Main entry point for PhotonRender SketchUp Extension

require 'sketchup.rb'
require 'extensions.rb'

module PhotonRender
  
  # Extension information
  PLUGIN_ID = 'photon_render'
  PLUGIN_NAME = 'PhotonRender'
  PLUGIN_VERSION = '0.1.0'
  PLUGIN_DESCRIPTION = 'Professional photorealistic rendering for SketchUp'
  PLUGIN_AUTHOR = 'Your Name'
  PLUGIN_COPYRIGHT = '© 2024'
  
  # Paths
  PLUGIN_DIR = File.dirname(__FILE__)
  PLUGIN_PATH = File.join(PLUGIN_DIR, 'photon_render')
  
  # Create extension
  unless file_loaded?(__FILE__)
    ex = SketchupExtension.new(PLUGIN_NAME, File.join(PLUGIN_PATH, 'main'))
    ex.description = PLUGIN_DESCRIPTION
    ex.version = PLUGIN_VERSION
    ex.copyright = PLUGIN_COPYRIGHT
    ex.creator = PLUGIN_AUTHOR
    Sketchup.register_extension(ex, true)
    file_loaded(__FILE__)
  end
  
end # module PhotonRender

# src/ruby/photon_render/main.rb
# Main plugin loader

module PhotonRender
  
  # Load Ruby C extension
  require File.join(PLUGIN_PATH, 'photon_core.so') # Windows: .dll, Mac: .bundle
  
  # Load plugin components
  require File.join(PLUGIN_PATH, 'menu')
  require File.join(PLUGIN_PATH, 'toolbar')
  require File.join(PLUGIN_PATH, 'render_manager')
  require File.join(PLUGIN_PATH, 'scene_export')
  require File.join(PLUGIN_PATH, 'viewport_tool')
  require File.join(PLUGIN_PATH, 'dialog')
  require File.join(PLUGIN_PATH, 'material_library_manager')
  require File.join(PLUGIN_PATH, 'texture_assignment_manager')
  require File.join(PLUGIN_PATH, 'material_validation_manager')
  require File.join(PLUGIN_PATH, 'material_export_import_manager')
  
  # Initialize plugin
  def self.initialize_plugin
    # Create menu items
    Menu.create

    # Create toolbar
    Toolbar.create

    # Initialize material library manager
    MaterialLibraryManager.initialize

    # Initialize texture assignment manager
    TextureAssignmentManager.initialize

    # Initialize material validation manager
    MaterialValidationManager.initialize

    # Initialize material export/import manager
    MaterialExportImportManager.initialize

    # Register observers
    Sketchup.active_model.add_observer(ModelObserver.new)

    # Load preferences
    load_preferences

    puts "PhotonRender #{PLUGIN_VERSION} loaded successfully"
  end
  
  # Load user preferences
  def self.load_preferences
    @preferences = {
      last_render_settings: Sketchup.read_default(PLUGIN_ID, 'render_settings', {}),
      viewport_preview: Sketchup.read_default(PLUGIN_ID, 'viewport_preview', true),
      auto_save_render: Sketchup.read_default(PLUGIN_ID, 'auto_save', false)
    }
  end
  
  # Save preferences
  def self.save_preferences
    Sketchup.write_default(PLUGIN_ID, 'render_settings', @preferences[:last_render_settings])
    Sketchup.write_default(PLUGIN_ID, 'viewport_preview', @preferences[:viewport_preview])
    Sketchup.write_default(PLUGIN_ID, 'auto_save', @preferences[:auto_save_render])
  end
  
  # Model observer
  class ModelObserver < Sketchup::ModelObserver
    def onTransactionCommit(model)
      # Update viewport preview if enabled
      ViewportTool.update_preview if PhotonRender.viewport_preview_enabled?
    end
  end
  
  # Initialize when loaded
  initialize_plugin
  
end # module PhotonRender

# src/ruby/photon_render/render_manager.rb
# Render management and control

module PhotonRender
  
  class RenderManager
    attr_reader :current_render, :is_rendering
    
    def initialize
      @current_render = nil
      @is_rendering = false
      @render_thread = nil
    end
    
    # Start new render
    def start_render(options = {})
      return if @is_rendering
      
      # Get current model
      model = Sketchup.active_model
      return UI.messagebox("No active model") unless model
      
      # Export scene data
      scene_data = SceneExport.export_scene(model)
      
      # Merge with default settings
      settings = default_settings.merge(options)
      
      # Create native render object
      @current_render = PhotonCore::Render.new
      @current_render.set_scene(scene_data)
      @current_render.set_settings(settings)
      
      # Setup callbacks
      setup_callbacks
      
      # Start render in separate thread
      @is_rendering = true
      @render_thread = Thread.new { render_thread_proc }
      
      # Show render dialog
      Dialog.show_render_progress
    end
    
    # Stop current render
    def stop_render
      return unless @is_rendering
      
      @current_render.stop if @current_render
      @render_thread.join if @render_thread
      @is_rendering = false
      
      Dialog.hide_render_progress
    end
    
    # Get current progress
    def progress
      return 0.0 unless @current_render
      @current_render.progress
    end
    
    # Save render result
    def save_render(filename)
      return unless @current_render && @current_render.is_complete?
      
      # Get pixel data
      pixels = @current_render.get_pixels
      width = @current_render.width
      height = @current_render.height
      
      # Save to file
      save_image(filename, pixels, width, height)
      
      UI.messagebox("Render saved to: #{filename}")
    end
    
    private
    
    # Default render settings
    def default_settings
      {
        width: 1920,
        height: 1080,
        samples_per_pixel: 100,
        max_bounces: 8,
        use_gpu: has_gpu?,
        enable_denoising: true,
        tile_size: 64
      }
    end
    
    # Check for GPU availability
    def has_gpu?
      PhotonCore.has_cuda_device? || PhotonCore.has_optix_device?
    end
    
    # Setup render callbacks
    def setup_callbacks
      # Progress callback
      @current_render.on_progress do |progress, stats|
        # Update UI in main thread
        UI.start_timer(0.0, false) do
          Dialog.update_progress(progress, stats)
        end
      end
      
      # Tile callback
      @current_render.on_tile_complete do |x, y, width, height, pixels|
        # Update viewport in main thread
        UI.start_timer(0.0, false) do
          ViewportTool.update_tile(x, y, width, height, pixels)
        end
      end
      
      # Completion callback
      @current_render.on_complete do
        UI.start_timer(0.0, false) do
          on_render_complete
        end
      end
    end
    
    # Render thread procedure
    def render_thread_proc
      begin
        @current_render.render
      rescue => e
        UI.messagebox("Render error: #{e.message}")
        puts e.backtrace.join("\n")
      ensure
        @is_rendering = false
      end
    end
    
    # Handle render completion
    def on_render_complete
      @is_rendering = false
      
      # Auto-save if enabled
      if PhotonRender.auto_save_enabled?
        timestamp = Time.now.strftime("%Y%m%d_%H%M%S")
        filename = "photon_render_#{timestamp}.png"
        save_render(filename)
      end
      
      # Show completion dialog
      result = UI.messagebox("Render complete! Save result?", MB_YESNO)
      if result == IDYES
        filename = UI.savepanel("Save Render", "", "*.png|*.jpg|*.exr")
        save_render(filename) if filename
      end
      
      Dialog.hide_render_progress
    end
    
    # Save image to file
    def save_image(filename, pixels, width, height)
      ext = File.extname(filename).downcase
      
      case ext
      when '.png'
        PhotonCore.save_png(filename, pixels, width, height)
      when '.jpg', '.jpeg'
        PhotonCore.save_jpeg(filename, pixels, width, height, 95)
      when '.exr'
        PhotonCore.save_exr(filename, pixels, width, height)
      else
        raise "Unsupported image format: #{ext}"
      end
    end
  end
  
  # Global render manager instance
  @render_manager = RenderManager.new
  
  def self.render_manager
    @render_manager
  end
  
end # module PhotonRender

# src/ruby/photon_render/scene_export.rb
# Scene export functionality

module PhotonRender
  
  module SceneExport
    
    # Export complete scene from SketchUp model
    def self.export_scene(model)
      scene = {
        camera: export_camera(model.active_view),
        geometry: export_geometry(model),
        materials: export_materials(model),
        lights: export_lights(model),
        environment: export_environment(model)
      }
      
      # Convert to format expected by C++ core
      PhotonCore::SceneData.new(scene)
    end
    
    private
    
    # Export camera data
    def self.export_camera(view)
      camera = view.camera
      eye = camera.eye.to_a
      target = camera.target.to_a
      up = camera.up.to_a
      fov = camera.fov
      aspect = view.vpwidth.to_f / view.vpheight.to_f
      
      {
        position: eye,
        target: target,
        up: up,
        fov: fov,
        aspect: aspect,
        near: 0.1,
        far: 10000.0
      }
    end
    
    # Export all geometry
    def self.export_geometry(model)
      meshes = []
      transformation_stack = [Geom::Transformation.new]
      
      # Recursively export entities
      export_entities(model.entities, meshes, transformation_stack)
      
      meshes
    end
    
    # Export entities recursively
    def self.export_entities(entities, meshes, transform_stack)
      entities.each do |entity|
        case entity
        when Sketchup::Group
          transform_stack.push(transform_stack.last * entity.transformation)
          export_entities(entity.entities, meshes, transform_stack)
          transform_stack.pop
          
        when Sketchup::ComponentInstance
          transform_stack.push(transform_stack.last * entity.transformation)
          export_entities(entity.definition.entities, meshes, transform_stack)
          transform_stack.pop
          
        when Sketchup::Face
          export_face(entity, meshes, transform_stack.last)
        end
      end
    end
    
    # Export single face
    def self.export_face(face, meshes, transform)
      mesh = face.mesh(7) # Get triangulated mesh with normals and UVs
      
      # Extract vertices
      vertices = []
      normals = []
      uvs = []
      
      (1..mesh.count_points).each do |i|
        point = mesh.point_at(i)
        point = transform * point
        vertices << point.to_a
        
        # Get vertex normal
        normal = mesh.normal_at(i)
        normal = transform.rotation * normal
        normals << normal.to_a
      end
      
      # Extract UVs if textured
      if face.material && face.material.texture
        tw = face.material.texture.width
        th = face.material.texture.height
        
        face.mesh.uvs(true).each do |uv|
          uvs << [uv.x / tw, 1.0 - (uv.y / th)]
        end
      else
        vertices.size.times { uvs << [0.0, 0.0] }
      end
      
      # Extract